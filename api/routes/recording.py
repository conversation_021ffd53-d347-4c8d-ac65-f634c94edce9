"""
Enhanced SOAP Generation API - Clean endpoints for audio and text processing
"""
import logging
from datetime import datetime, timezone
from typing import Dict, Any

from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import json

from services.processing_service import ProcessingService
from utils.serialization import create_api_response, safe_json_response, EnhancedJSONEncoder

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize processing service
processing_service = ProcessingService()

class CustomJSONResponse(JSONResponse):
    """Custom JSONResponse that uses our EnhancedJSONEncoder"""
    def render(self, content) -> bytes:
        return json.dumps(
            content,
            cls=EnhancedJSONEncoder,
            ensure_ascii=False,
            allow_nan=False,
            indent=None,
            separators=(",", ":"),
        ).encode("utf-8")

class TextProcessRequest(BaseModel):
    """Request model for text processing"""
    text: str
    patient_id: str

@router.post("/process-audio")
async def process_audio(
    audio_file: UploadFile = File(...),
    patient_id: str = Form(...)
) -> CustomJSONResponse:
    """
    Process audio file through enhanced SOAP generation pipeline

    Args:
        audio_file: Audio file to process (.mp3, .wav, .m4a, .flac)
        patient_id: Patient identifier

    Returns:
        Enhanced SOAP notes with quality metrics and safety assessment
    """
    try:
        logger.info(f"🎤 Enhanced audio processing started for patient {patient_id}")

        # Validate file
        if not audio_file.filename:
            raise HTTPException(status_code=400, detail="No audio file provided")

        if not audio_file.filename.lower().endswith(('.mp3', '.wav', '.m4a', '.flac')):
            raise HTTPException(status_code=400, detail="Unsupported audio format. Use .mp3, .wav, .m4a, or .flac")

        # Process through enhanced pipeline
        result = await processing_service.process_audio_complete_flow(
            audio_file=audio_file,
            session_id=f"audio_{patient_id}_{int(datetime.now().timestamp())}",
            patient_id=patient_id,
            specialty="general",  # Will be auto-detected by specialty detection agent
            doctor_id="api_user"
        )

        return CustomJSONResponse(content=create_api_response(
            status="success",
            message="Enhanced audio processing completed successfully",
            data=result
        ))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Enhanced audio processing failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Enhanced audio processing failed: {str(e)}")

@router.post("/process-text")
async def process_text(request: TextProcessRequest) -> CustomJSONResponse:
    """
    Process transcribed text through enhanced SOAP generation pipeline

    Args:
        request: Text processing request with transcribed text and patient ID

    Returns:
        Enhanced SOAP notes with quality metrics and safety assessment
    """
    try:
        logger.info(f"📝 Enhanced text processing started for patient {request.patient_id}")

        # Validate text
        if not request.text or len(request.text.strip()) < 10:
            raise HTTPException(status_code=400, detail="Text must be at least 10 characters long")

        # Create a mock transcription result for the text processing pipeline
        from models.schemas import TranscriptionResult
        mock_transcription = TranscriptionResult(
            text=request.text,
            confidence=1.0,  # Perfect confidence for direct text input
            duration=0.0,
            language="en"
        )

        # Process through enhanced pipeline starting from validation
        session_id = f"text_{request.patient_id}_{int(datetime.now().timestamp())}"

        try:
            # Step 1: Medical Validation
            logger.info(f"Step 1: Validating medical terminology for session {session_id}")
            validation_result = await processing_service.validation_agent.validate_medical_terminology(
                request.text, request.patient_id, "general"
            )

            # Step 2: Detect specialty dynamically
            logger.info(f"Step 2: Detecting medical specialty for session {session_id}")
            specialty_config = await processing_service.specialty_detection_agent.detect_specialty(
                validation_result.validated_text
            )

            # Step 3: Generate structured SOAP notes
            logger.info(f"Step 3: Generating structured SOAP notes for session {session_id}")
            structured_soap_notes = await processing_service.soap_agent.generate_soap_notes(
                validation_result.validated_text,
                request.patient_id,
                specialty_config,
                session_id,
                validation_result.flags
            )

            # Step 4: Enhance with clinical reasoning
            logger.info(f"Step 4: Adding clinical reasoning for session {session_id}")
            enhanced_soap_notes = await processing_service.clinical_reasoning_agent.enhance_assessment(
                structured_soap_notes,
                validation_result.validated_text,
                specialty_config
            )

            # Step 5: Calculate quality metrics
            logger.info(f"Step 5: Calculating quality metrics for session {session_id}")
            quality_metrics = await processing_service.quality_metrics_agent.calculate_quality_metrics(
                enhanced_soap_notes,
                validation_result.validated_text,
                specialty_config
            )

            # Step 6: Create complete SOAP notes with quality metrics
            from models.schemas import SOAPNotes
            complete_soap_notes = SOAPNotes(
                soap_notes=enhanced_soap_notes,
                quality_metrics=quality_metrics,
                session_id=session_id,
                specialty=specialty_config.specialty
            )

            # Step 7: Perform safety check
            logger.info(f"Step 7: Performing safety check for session {session_id}")
            safety_checked_notes, safety_result = await processing_service.safety_check_agent.perform_safety_check(
                complete_soap_notes, specialty_config
            )

            # Step 8: Apply final formatting
            logger.info(f"Step 8: Applying final formatting for session {session_id}")
            final_soap_notes = await processing_service.final_formatting_agent.format_final_notes(
                safety_checked_notes, specialty_config
            )

            # Create response
            result = {
                "status": "completed",
                "message": "Enhanced text processing completed successfully",
                "session_id": session_id,
                "transcription": mock_transcription,
                "validation": validation_result,
                "specialty_detection": {
                    "detected_specialty": specialty_config.specialty,
                    "confidence": specialty_config.confidence,
                    "focus_areas": specialty_config.focus_areas
                },
                "soap_notes": final_soap_notes,
                "quality_metrics": {
                    "completeness_score": final_soap_notes.quality_metrics.completeness_score,
                    "clinical_accuracy": final_soap_notes.quality_metrics.clinical_accuracy,
                    "documentation_quality": final_soap_notes.quality_metrics.documentation_quality,
                    "red_flags": final_soap_notes.quality_metrics.red_flags,
                    "missing_information": final_soap_notes.quality_metrics.missing_information
                },
                "safety_check": {
                    "is_safe": safety_result.is_safe if safety_result else True,
                    "red_flags": safety_result.red_flags if safety_result else [],
                    "critical_items": safety_result.critical_items if safety_result else []
                },
                "enhanced_pipeline": True
            }

            return CustomJSONResponse(content=create_api_response(
                status="success",
                message="Enhanced text processing completed successfully",
                data=result
            ))

        except Exception as e:
            logger.error(f"Enhanced text processing failed for session {session_id}: {e}", exc_info=True)
            raise e

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Enhanced text processing failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Enhanced text processing failed: {str(e)}")
