"""
SOAP Notes Parsing Utilities
Robust JSON parsing for complex nested SOAP structure with validation and fallback mechanisms
"""
import json
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import asdict

from models.schemas import (
    SOAPNotes, SOAPNotesStructured, SubjectiveSection, ObjectiveSection,
    AssessmentSection, PlanSection, PrimaryDiagnosis, DifferentialDiagnosis,
    ProblemListItem, FollowUpItem, QualityMetrics
)

logger = logging.getLogger(__name__)

class SOAPParsingError(Exception):
    """Custom exception for SOAP parsing errors"""
    pass

class SOAPParser:
    """Robust parser for SOAP notes with validation and fallback mechanisms"""
    
    def __init__(self):
        self.validation_errors = []
        self.warnings = []
    
    def parse_enhanced_soap_response(
        self,
        response_text: str,
        session_id: str,
        specialty: str,
        fallback_data: Optional[Dict[str, Any]] = None
    ) -> SOAPNotesStructured:
        """
        Parse LLM response into SOAPNotesStructured with robust error handling
        
        Args:
            response_text: Raw LLM response text
            session_id: Session identifier for logging
            specialty: Medical specialty for context
            fallback_data: Optional fallback data if parsing fails
            
        Returns:
            SOAPNotesStructured object
        """
        self.validation_errors = []
        self.warnings = []
        
        try:
            # Extract JSON from response
            json_data = self._extract_json_from_response(response_text)
            
            # Validate structure
            self._validate_soap_structure(json_data)
            
            # Parse sections
            structured_soap = self._parse_soap_sections(json_data)
            
            logger.info(f"Successfully parsed enhanced SOAP notes for session {session_id}")
            if self.warnings:
                logger.warning(f"Parsing warnings for session {session_id}: {self.warnings}")
            
            return structured_soap
            
        except Exception as e:
            logger.error(f"Failed to parse SOAP response for session {session_id}: {e}")
            logger.debug(f"Response text: {response_text[:500]}...")
            
            # Try fallback parsing
            return self._create_fallback_soap_notes(fallback_data, session_id, specialty)
    
    def _extract_json_from_response(self, response_text: str) -> Dict[str, Any]:
        """Extract JSON from LLM response text"""
        # Try direct JSON parsing first
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            pass
        
        # Try to find JSON within the response
        json_start = response_text.find('{')
        json_end = response_text.rfind('}') + 1
        
        if json_start >= 0 and json_end > json_start:
            json_str = response_text[json_start:json_end]
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                pass
        
        # Try to extract JSON blocks
        import re
        json_pattern = r'```json\s*(.*?)\s*```'
        matches = re.findall(json_pattern, response_text, re.DOTALL)
        
        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue
        
        raise SOAPParsingError("No valid JSON found in response")
    
    def _validate_soap_structure(self, data: Dict[str, Any]) -> None:
        """Validate the structure of parsed SOAP data"""
        required_sections = ['subjective', 'objective', 'assessment', 'plan']
        
        for section in required_sections:
            if section not in data:
                self.validation_errors.append(f"Missing required section: {section}")
        
        # Validate subjective section
        if 'subjective' in data:
            subj = data['subjective']
            if not isinstance(subj, dict):
                self.validation_errors.append("Subjective section must be a dictionary")
            else:
                required_subj_fields = ['chief_complaint', 'history_present_illness']
                for field in required_subj_fields:
                    if field not in subj:
                        self.warnings.append(f"Missing subjective field: {field}")
        
        # Validate objective section
        if 'objective' in data:
            obj = data['objective']
            if not isinstance(obj, dict):
                self.validation_errors.append("Objective section must be a dictionary")
        
        # Validate assessment section
        if 'assessment' in data:
            assess = data['assessment']
            if not isinstance(assess, dict):
                self.validation_errors.append("Assessment section must be a dictionary")
            else:
                if 'primary_diagnosis' not in assess:
                    self.validation_errors.append("Missing primary_diagnosis in assessment")
        
        # Validate plan section
        if 'plan' in data:
            plan = data['plan']
            if not isinstance(plan, dict):
                self.validation_errors.append("Plan section must be a dictionary")
        
        if self.validation_errors:
            raise SOAPParsingError(f"Validation errors: {'; '.join(self.validation_errors)}")
    
    def _parse_soap_sections(self, data: Dict[str, Any]) -> SOAPNotesStructured:
        """Parse individual SOAP sections"""
        # Parse subjective section
        subjective = self._parse_subjective_section(data.get('subjective', {}))
        
        # Parse objective section
        objective = self._parse_objective_section(data.get('objective', {}))
        
        # Parse assessment section
        assessment = self._parse_assessment_section(data.get('assessment', {}))
        
        # Parse plan section
        plan = self._parse_plan_section(data.get('plan', {}))
        
        # Get clinical notes
        clinical_notes = data.get('clinical_notes', '')
        
        return SOAPNotesStructured(
            subjective=subjective,
            objective=objective,
            assessment=assessment,
            plan=plan,
            clinical_notes=clinical_notes
        )
    
    def _parse_subjective_section(self, data: Dict[str, Any]) -> SubjectiveSection:
        """Parse subjective section with safe defaults"""
        return SubjectiveSection(
            chief_complaint=self._safe_get_string(data, 'chief_complaint', ''),
            history_present_illness=self._safe_get_string(data, 'history_present_illness', ''),
            review_of_systems=self._safe_get_list(data, 'review_of_systems', []),
            past_medical_history=self._safe_get_list(data, 'past_medical_history', []),
            medications=self._safe_get_list(data, 'medications', []),
            allergies=self._safe_get_list(data, 'allergies', []),
            social_history=self._safe_get_string(data, 'social_history', '')
        )
    
    def _parse_objective_section(self, data: Dict[str, Any]) -> ObjectiveSection:
        """Parse objective section with safe defaults"""
        return ObjectiveSection(
            vital_signs=self._safe_get_dict(data, 'vital_signs', {}),
            physical_exam=self._safe_get_dict(data, 'physical_exam', {}),
            diagnostic_results=self._safe_get_list(data, 'diagnostic_results', []),
            mental_status=self._safe_get_string(data, 'mental_status', ''),
            functional_status=self._safe_get_string(data, 'functional_status', '')
        )
    
    def _parse_assessment_section(self, data: Dict[str, Any]) -> AssessmentSection:
        """Parse assessment section with safe defaults"""
        # Parse primary diagnosis
        primary_data = data.get('primary_diagnosis', {})
        primary_diagnosis = PrimaryDiagnosis(
            diagnosis=self._safe_get_string(primary_data, 'diagnosis', 'Unspecified'),
            icd10_code=self._safe_get_string(primary_data, 'icd10_code', ''),
            confidence=self._safe_get_float(primary_data, 'confidence', 0.5),
            severity=self._safe_get_string(primary_data, 'severity', 'moderate'),
            clinical_reasoning=self._safe_get_string(primary_data, 'clinical_reasoning', '')
        )
        
        # Parse differential diagnoses
        differentials = []
        for diff_data in data.get('differential_diagnoses', []):
            if isinstance(diff_data, dict):
                differentials.append(DifferentialDiagnosis(
                    diagnosis=self._safe_get_string(diff_data, 'diagnosis', ''),
                    icd10_code=self._safe_get_string(diff_data, 'icd10_code', ''),
                    probability=self._safe_get_float(diff_data, 'probability', 0.0),
                    ruling_out_criteria=self._safe_get_string(diff_data, 'ruling_out_criteria', '')
                ))
        
        # Parse problem list
        problems = []
        for prob_data in data.get('problem_list', []):
            if isinstance(prob_data, dict):
                problems.append(ProblemListItem(
                    problem=self._safe_get_string(prob_data, 'problem', ''),
                    status=self._safe_get_string(prob_data, 'status', 'active'),
                    priority=self._safe_get_string(prob_data, 'priority', 'medium')
                ))
        
        return AssessmentSection(
            primary_diagnosis=primary_diagnosis,
            differential_diagnoses=differentials,
            problem_list=problems,
            risk_level=self._safe_get_string(data, 'risk_level', 'moderate'),
            risk_factors=self._safe_get_list(data, 'risk_factors', []),
            prognosis=self._safe_get_string(data, 'prognosis', '')
        )
    
    def _parse_plan_section(self, data: Dict[str, Any]) -> PlanSection:
        """Parse plan section with safe defaults"""
        # Parse follow-up items
        follow_ups = []
        for follow_data in data.get('follow_up', []):
            if isinstance(follow_data, dict):
                follow_ups.append(FollowUpItem(
                    provider=self._safe_get_string(follow_data, 'provider', ''),
                    timeframe=self._safe_get_string(follow_data, 'timeframe', ''),
                    urgency=self._safe_get_string(follow_data, 'urgency', 'routine')
                ))
        
        return PlanSection(
            diagnostic_workup=self._safe_get_list(data, 'diagnostic_workup', []),
            treatments=self._safe_get_list(data, 'treatments', []),
            medications=self._safe_get_list(data, 'medications', []),
            follow_up=follow_ups,
            patient_education=self._safe_get_list(data, 'patient_education', []),
            referrals=self._safe_get_list(data, 'referrals', [])
        )
    
    def _safe_get_string(self, data: Dict[str, Any], key: str, default: str) -> str:
        """Safely get string value with default"""
        value = data.get(key, default)
        return str(value) if value is not None else default
    
    def _safe_get_list(self, data: Dict[str, Any], key: str, default: List[Any]) -> List[Any]:
        """Safely get list value with default"""
        value = data.get(key, default)
        if isinstance(value, list):
            return value
        elif isinstance(value, str) and value:
            return [value]  # Convert single string to list
        else:
            return default
    
    def _safe_get_dict(self, data: Dict[str, Any], key: str, default: Dict[str, Any]) -> Dict[str, Any]:
        """Safely get dictionary value with default"""
        value = data.get(key, default)
        return value if isinstance(value, dict) else default
    
    def _safe_get_float(self, data: Dict[str, Any], key: str, default: float) -> float:
        """Safely get float value with default"""
        value = data.get(key, default)
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    
    def _create_fallback_soap_notes(
        self,
        fallback_data: Optional[Dict[str, Any]],
        session_id: str,
        specialty: str
    ) -> SOAPNotesStructured:
        """Create fallback SOAP notes when parsing fails"""
        logger.warning(f"Creating fallback SOAP notes for session {session_id}")
        
        # Create basic sections with error messages
        subjective = SubjectiveSection(
            chief_complaint="Unable to extract chief complaint due to parsing error",
            history_present_illness="Unable to extract history of present illness",
            review_of_systems=["Parsing error - unable to extract review of systems"],
            past_medical_history=["Parsing error - unable to extract past medical history"],
            medications=["Parsing error - unable to extract medications"],
            allergies=["Parsing error - unable to extract allergies"],
            social_history="Unable to extract social history due to parsing error"
        )
        
        objective = ObjectiveSection(
            vital_signs={"error": "Unable to extract vital signs due to parsing error"},
            physical_exam={"error": "Unable to extract physical exam findings"},
            diagnostic_results=["Parsing error - unable to extract diagnostic results"],
            mental_status="Unable to extract mental status",
            functional_status="Unable to extract functional status"
        )
        
        primary_diagnosis = PrimaryDiagnosis(
            diagnosis="Unable to determine diagnosis due to parsing error",
            icd10_code="",
            confidence=0.0,
            severity="unknown",
            clinical_reasoning="Clinical reasoning could not be extracted due to parsing error"
        )
        
        assessment = AssessmentSection(
            primary_diagnosis=primary_diagnosis,
            differential_diagnoses=[],
            problem_list=[],
            risk_level="unknown",
            risk_factors=["Unable to assess risk factors due to parsing error"],
            prognosis="Unable to determine prognosis due to parsing error"
        )
        
        plan = PlanSection(
            diagnostic_workup=["Parsing error - unable to extract diagnostic workup"],
            treatments=["Parsing error - unable to extract treatments"],
            medications=["Parsing error - unable to extract medications"],
            follow_up=[],
            patient_education=["Parsing error - unable to extract patient education"],
            referrals=["Parsing error - unable to extract referrals"]
        )
        
        return SOAPNotesStructured(
            subjective=subjective,
            objective=objective,
            assessment=assessment,
            plan=plan,
            clinical_notes=f"Error: SOAP notes could not be parsed for session {session_id}. Manual review required."
        )
