"""
Medical Transcription Validation Agent
"""
import json
import logging
from typing import List, Dict, Any

from langchain_openai import ChatOpenA<PERSON>
from langchain_core.messages import HumanMessage, SystemMessage

from config.settings import settings
from models.schemas import ValidationResult

logger = logging.getLogger(__name__)

class MedicalTranscriptionAgent:
    """Agent for validating and correcting medical terminology in transcriptions"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.openai_model,
            temperature=settings.openai_temperature,
            max_tokens=settings.openai_max_tokens,
            openai_api_key=settings.openai_api_key
        )
    
    async def validate_medical_terminology(
        self, 
        text: str, 
        patient_id: str, 
        specialty: str
    ) -> ValidationResult:
        """
        Validate medical terminology accuracy and identify potential errors
        
        Args:
            text: Raw transcription text
            patient_id: Patient identifier
            specialty: Medical specialty context
            
        Returns:
            ValidationResult with corrected text and flags
        """
        system_prompt = self._get_validation_system_prompt()
        user_prompt = self._get_validation_user_prompt(text, patient_id, specialty)
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        try:
            response = await self.llm.agenerate([messages])
            result_text = response.generations[0][0].text
            result = self._parse_validation_response(result_text, text)
            
            logger.info(f"Validation completed - Confidence: {result.confidence}")
            return result
            
        except Exception as e:
            logger.error(f"Medical validation failed: {e}")
            return ValidationResult(
                validated_text=text,
                corrections=[],
                flags=[{"type": "error", "message": f"Validation failed: {str(e)}"}],
                confidence=0.5
            )
    
    def _get_validation_system_prompt(self) -> str:
        """Get the system prompt for medical validation"""
        return """You are a Medical Terminology Validation Agent. Your role is to:

1. Validate medical terminology accuracy
2. Identify potential transcription errors in medical terms
3. Suggest corrections for unclear medical terminology
4. Flag any concerning medical information
5. Ensure proper medical abbreviations and dosages

Analyze the transcription and return a JSON response with:
- validatedText: corrected transcription with proper medical terminology
- corrections: list of corrections made with original and corrected terms
- flags: list of concerning items that need attention
- confidence: confidence score 0-1 for the validation quality

Focus on:
- Medication names and dosages
- Medical procedures and diagnoses
- Anatomical terms
- Clinical abbreviations
- Vital signs and measurements
- Allergy information
- Critical safety concerns

Return only valid JSON without additional text."""
    
    def _get_validation_user_prompt(self, text: str, patient_id: str, specialty: str) -> str:
        """Get the user prompt for validation"""
        return f"""Validate this medical transcription:

Text: {text}
Patient ID: {patient_id}
Specialty: {specialty}

Please correct any medical terminology errors and flag important items."""
    
    def _parse_validation_response(self, response_text: str, original_text: str) -> ValidationResult:
        """Parse the LLM response into a ValidationResult"""
        try:
            result = json.loads(response_text)
            return ValidationResult(
                validated_text=result.get('validatedText', original_text),
                corrections=result.get('corrections', []),
                flags=result.get('flags', []),
                confidence=result.get('confidence', 0.8)
            )
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse validation response: {e}")
            logger.debug(f"Response text: {response_text}")
            return ValidationResult(
                validated_text=original_text,
                corrections=[],
                flags=[{"type": "error", "message": "Failed to parse validation response"}],
                confidence=0.5
            )
    
    def _extract_critical_terms(self, text: str) -> List[Dict[str, Any]]:
        """Extract critical medical terms that need special attention"""
        critical_patterns = {
            'allergies': ['allergic', 'allergy', 'allergies', 'anaphylaxis'],
            'medications': ['mg', 'ml', 'tablet', 'capsule', 'injection'],
            'urgent': ['emergency', 'urgent', 'stat', 'critical', 'severe'],
            'vitals': ['blood pressure', 'heart rate', 'temperature', 'oxygen']
        }
        
        critical_terms = []
        text_lower = text.lower()
        
        for category, terms in critical_patterns.items():
            for term in terms:
                if term in text_lower:
                    critical_terms.append({
                        'category': category,
                        'term': term,
                        'requires_attention': category in ['allergies', 'urgent']
                    })
        
        return critical_terms
