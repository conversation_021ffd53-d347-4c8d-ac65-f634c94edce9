"""
Final Formatting Agent
Performs final formatting and validation of the complete SOAP structure before database storage
"""
import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import asdict

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

from config.settings import settings
from models.schemas import SOAPNotes, SOAPNotesStructured, QualityMetrics
from agents.specialty_detection import SpecialtyConfiguration

logger = logging.getLogger(__name__)

class FinalFormattingAgent:
    """Agent for final formatting and validation of SOAP notes"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.openai_model,
            temperature=0.1,  # Low temperature for consistent formatting
            max_tokens=2000,
            openai_api_key=settings.openai_api_key
        )
    
    async def format_final_notes(
        self,
        soap_notes: SOAPNotes,
        specialty_config: SpecialtyConfiguration
    ) -> SOAPNotes:
        """
        Perform final formatting and validation of SOAP notes
        
        Args:
            soap_notes: Complete SOAP notes to format
            specialty_config: Detected specialty configuration
            
        Returns:
            Formatted and validated SOAP notes
        """
        system_prompt = self._get_formatting_system_prompt(specialty_config)
        user_prompt = self._get_formatting_user_prompt(soap_notes)
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        try:
            response = await self.llm.agenerate([messages])
            result_text = response.generations[0][0].text
            
            formatted_notes = self._parse_formatting_response(result_text, soap_notes)
            
            logger.info(f"Final formatting completed for {soap_notes.specialty} notes")
            return formatted_notes
            
        except Exception as e:
            logger.error(f"Final formatting failed: {e}")
            # Return original notes if formatting fails
            return soap_notes
    
    def _get_formatting_system_prompt(self, specialty_config: SpecialtyConfiguration) -> str:
        """Get system prompt for final formatting"""
        return f"""You are a Medical Documentation Expert specializing in {specialty_config.specialty.title()}. Your task is to perform final formatting and validation of SOAP notes before they are stored in the database.

SPECIALTY CONTEXT:
- Specialty: {specialty_config.specialty}
- Focus Areas: {', '.join(specialty_config.focus_areas)}
- Required Sections: {', '.join(specialty_config.required_sections)}

FORMATTING REQUIREMENTS:
1. Ensure all sections are complete and properly formatted
2. Standardize medical terminology and abbreviations
3. Correct any inconsistencies between sections
4. Add a concise clinical summary in the clinical_notes field
5. Ensure all fields have appropriate content
6. Validate that the structure follows the required format
7. Fill in any missing required fields with appropriate content

OUTPUT FORMAT:
Return ONLY valid JSON with the complete SOAP notes structure. Maintain the exact same structure as provided in the input, but with improved formatting and content where needed.

Be precise, clinically accurate, and maintain professional medical documentation standards."""
    
    def _get_formatting_user_prompt(self, soap_notes: SOAPNotes) -> str:
        """Get user prompt for final formatting"""
        # Convert SOAP notes to dictionary for easier JSON formatting
        soap_dict = self._soap_notes_to_dict(soap_notes)
        soap_json = json.dumps(soap_dict, indent=2)
        
        return f"""Please perform final formatting and validation on these SOAP notes:

```json
{soap_json}
```

Please review the entire structure, ensure all fields have appropriate content, and return the formatted SOAP notes in the same JSON structure.

Add a concise clinical summary in the clinical_notes field that captures the key elements of the case.

Return the complete formatted JSON structure."""
    
    def _parse_formatting_response(self, response_text: str, original_notes: SOAPNotes) -> SOAPNotes:
        """Parse LLM response into SOAPNotes object"""
        try:
            # Try to extract JSON from the response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response_text[json_start:json_end]
                result = json.loads(json_str)
                
                # Reconstruct SOAPNotes from the formatted JSON
                return self._dict_to_soap_notes(result, original_notes)
            else:
                logger.error("No valid JSON found in formatting response")
                return original_notes
                
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            logger.error(f"Failed to parse formatting response: {e}")
            logger.debug(f"Response text: {response_text}")
            return original_notes
    
    def _soap_notes_to_dict(self, soap_notes: SOAPNotes) -> Dict[str, Any]:
        """Convert SOAPNotes object to dictionary for JSON serialization"""
        try:
            # Use dataclasses.asdict for nested conversion
            return {
                "soap_notes": asdict(soap_notes.soap_notes),
                "quality_metrics": asdict(soap_notes.quality_metrics),
                "session_id": soap_notes.session_id,
                "specialty": soap_notes.specialty
            }
        except Exception as e:
            logger.error(f"Error converting SOAP notes to dict: {e}")
            # Fallback to manual conversion
            return {
                "soap_notes": {
                    "subjective": {
                        "chief_complaint": soap_notes.soap_notes.subjective.chief_complaint,
                        "history_present_illness": soap_notes.soap_notes.subjective.history_present_illness,
                        "review_of_systems": soap_notes.soap_notes.subjective.review_of_systems,
                        "past_medical_history": soap_notes.soap_notes.subjective.past_medical_history,
                        "medications": soap_notes.soap_notes.subjective.medications,
                        "allergies": soap_notes.soap_notes.subjective.allergies,
                        "social_history": soap_notes.soap_notes.subjective.social_history
                    },
                    "objective": {
                        "vital_signs": soap_notes.soap_notes.objective.vital_signs,
                        "physical_exam": soap_notes.soap_notes.objective.physical_exam,
                        "diagnostic_results": soap_notes.soap_notes.objective.diagnostic_results,
                        "mental_status": soap_notes.soap_notes.objective.mental_status,
                        "functional_status": soap_notes.soap_notes.objective.functional_status
                    },
                    "assessment": {
                        "primary_diagnosis": {
                            "diagnosis": soap_notes.soap_notes.assessment.primary_diagnosis.diagnosis,
                            "icd10_code": soap_notes.soap_notes.assessment.primary_diagnosis.icd10_code,
                            "confidence": soap_notes.soap_notes.assessment.primary_diagnosis.confidence,
                            "severity": soap_notes.soap_notes.assessment.primary_diagnosis.severity,
                            "clinical_reasoning": soap_notes.soap_notes.assessment.primary_diagnosis.clinical_reasoning
                        },
                        "differential_diagnoses": [
                            {
                                "diagnosis": d.diagnosis,
                                "icd10_code": d.icd10_code,
                                "probability": d.probability,
                                "ruling_out_criteria": d.ruling_out_criteria
                            } for d in soap_notes.soap_notes.assessment.differential_diagnoses
                        ],
                        "problem_list": [
                            {
                                "problem": p.problem,
                                "status": p.status,
                                "priority": p.priority
                            } for p in soap_notes.soap_notes.assessment.problem_list
                        ],
                        "risk_level": soap_notes.soap_notes.assessment.risk_level,
                        "risk_factors": soap_notes.soap_notes.assessment.risk_factors,
                        "prognosis": soap_notes.soap_notes.assessment.prognosis
                    },
                    "plan": {
                        "diagnostic_workup": soap_notes.soap_notes.plan.diagnostic_workup,
                        "treatments": soap_notes.soap_notes.plan.treatments,
                        "medications": soap_notes.soap_notes.plan.medications,
                        "follow_up": [
                            {
                                "provider": f.provider,
                                "timeframe": f.timeframe,
                                "urgency": f.urgency
                            } for f in soap_notes.soap_notes.plan.follow_up
                        ],
                        "patient_education": soap_notes.soap_notes.plan.patient_education,
                        "referrals": soap_notes.soap_notes.plan.referrals
                    },
                    "clinical_notes": soap_notes.soap_notes.clinical_notes
                },
                "quality_metrics": {
                    "completeness_score": soap_notes.quality_metrics.completeness_score,
                    "clinical_accuracy": soap_notes.quality_metrics.clinical_accuracy,
                    "documentation_quality": soap_notes.quality_metrics.documentation_quality,
                    "red_flags": soap_notes.quality_metrics.red_flags,
                    "missing_information": soap_notes.quality_metrics.missing_information
                },
                "session_id": soap_notes.session_id,
                "specialty": soap_notes.specialty
            }
    
    def _dict_to_soap_notes(self, data: Dict[str, Any], original_notes: SOAPNotes) -> SOAPNotes:
        """Convert dictionary back to SOAPNotes object"""
        from models.schemas import (
            SubjectiveSection, ObjectiveSection, PrimaryDiagnosis,
            DifferentialDiagnosis, ProblemListItem, AssessmentSection,
            FollowUpItem, PlanSection, QualityMetrics, SOAPNotesStructured
        )
        
        try:
            # Extract data with fallbacks to original values
            soap_data = data.get("soap_notes", {})
            quality_data = data.get("quality_metrics", {})
            
            # Reconstruct subjective section
            subj_data = soap_data.get("subjective", {})
            subjective = SubjectiveSection(
                chief_complaint=subj_data.get("chief_complaint", original_notes.soap_notes.subjective.chief_complaint),
                history_present_illness=subj_data.get("history_present_illness", original_notes.soap_notes.subjective.history_present_illness),
                review_of_systems=subj_data.get("review_of_systems", original_notes.soap_notes.subjective.review_of_systems),
                past_medical_history=subj_data.get("past_medical_history", original_notes.soap_notes.subjective.past_medical_history),
                medications=subj_data.get("medications", original_notes.soap_notes.subjective.medications),
                allergies=subj_data.get("allergies", original_notes.soap_notes.subjective.allergies),
                social_history=subj_data.get("social_history", original_notes.soap_notes.subjective.social_history)
            )
            
            # Reconstruct objective section
            obj_data = soap_data.get("objective", {})
            objective = ObjectiveSection(
                vital_signs=obj_data.get("vital_signs", original_notes.soap_notes.objective.vital_signs),
                physical_exam=obj_data.get("physical_exam", original_notes.soap_notes.objective.physical_exam),
                diagnostic_results=obj_data.get("diagnostic_results", original_notes.soap_notes.objective.diagnostic_results),
                mental_status=obj_data.get("mental_status", original_notes.soap_notes.objective.mental_status),
                functional_status=obj_data.get("functional_status", original_notes.soap_notes.objective.functional_status)
            )
            
            # Reconstruct assessment section
            assess_data = soap_data.get("assessment", {})
            primary_data = assess_data.get("primary_diagnosis", {})
            primary_diagnosis = PrimaryDiagnosis(
                diagnosis=primary_data.get("diagnosis", original_notes.soap_notes.assessment.primary_diagnosis.diagnosis),
                icd10_code=primary_data.get("icd10_code", original_notes.soap_notes.assessment.primary_diagnosis.icd10_code),
                confidence=float(primary_data.get("confidence", original_notes.soap_notes.assessment.primary_diagnosis.confidence)),
                severity=primary_data.get("severity", original_notes.soap_notes.assessment.primary_diagnosis.severity),
                clinical_reasoning=primary_data.get("clinical_reasoning", original_notes.soap_notes.assessment.primary_diagnosis.clinical_reasoning)
            )
            
            # Reconstruct differential diagnoses
            diff_diagnoses = []
            for diff_data in assess_data.get("differential_diagnoses", []):
                diff_diagnoses.append(DifferentialDiagnosis(
                    diagnosis=diff_data.get("diagnosis", ""),
                    icd10_code=diff_data.get("icd10_code", ""),
                    probability=float(diff_data.get("probability", 0.0)),
                    ruling_out_criteria=diff_data.get("ruling_out_criteria", "")
                ))
            
            # Reconstruct problem list
            problems = []
            for prob_data in assess_data.get("problem_list", []):
                problems.append(ProblemListItem(
                    problem=prob_data.get("problem", ""),
                    status=prob_data.get("status", "active"),
                    priority=prob_data.get("priority", "medium")
                ))
            
            # Create assessment section
            assessment = AssessmentSection(
                primary_diagnosis=primary_diagnosis,
                differential_diagnoses=diff_diagnoses if diff_diagnoses else original_notes.soap_notes.assessment.differential_diagnoses,
                problem_list=problems if problems else original_notes.soap_notes.assessment.problem_list,
                risk_level=assess_data.get("risk_level", original_notes.soap_notes.assessment.risk_level),
                risk_factors=assess_data.get("risk_factors", original_notes.soap_notes.assessment.risk_factors),
                prognosis=assess_data.get("prognosis", original_notes.soap_notes.assessment.prognosis)
            )
            
            # Reconstruct plan section
            plan_data = soap_data.get("plan", {})
            follow_ups = []
            for follow_data in plan_data.get("follow_up", []):
                follow_ups.append(FollowUpItem(
                    provider=follow_data.get("provider", ""),
                    timeframe=follow_data.get("timeframe", ""),
                    urgency=follow_data.get("urgency", "routine")
                ))
            
            plan = PlanSection(
                diagnostic_workup=plan_data.get("diagnostic_workup", original_notes.soap_notes.plan.diagnostic_workup),
                treatments=plan_data.get("treatments", original_notes.soap_notes.plan.treatments),
                medications=plan_data.get("medications", original_notes.soap_notes.plan.medications),
                follow_up=follow_ups if follow_ups else original_notes.soap_notes.plan.follow_up,
                patient_education=plan_data.get("patient_education", original_notes.soap_notes.plan.patient_education),
                referrals=plan_data.get("referrals", original_notes.soap_notes.plan.referrals)
            )
            
            # Create structured SOAP notes
            structured_notes = SOAPNotesStructured(
                subjective=subjective,
                objective=objective,
                assessment=assessment,
                plan=plan,
                clinical_notes=soap_data.get("clinical_notes", original_notes.soap_notes.clinical_notes)
            )
            
            # Reconstruct quality metrics
            quality_metrics = QualityMetrics(
                completeness_score=float(quality_data.get("completeness_score", original_notes.quality_metrics.completeness_score)),
                clinical_accuracy=float(quality_data.get("clinical_accuracy", original_notes.quality_metrics.clinical_accuracy)),
                documentation_quality=float(quality_data.get("documentation_quality", original_notes.quality_metrics.documentation_quality)),
                red_flags=quality_data.get("red_flags", original_notes.quality_metrics.red_flags),
                missing_information=quality_data.get("missing_information", original_notes.quality_metrics.missing_information)
            )
            
            # Create final SOAP notes
            return SOAPNotes(
                soap_notes=structured_notes,
                quality_metrics=quality_metrics,
                session_id=data.get("session_id", original_notes.session_id),
                specialty=data.get("specialty", original_notes.specialty)
            )
            
        except Exception as e:
            logger.error(f"Error converting dict to SOAP notes: {e}")
            return original_notes
