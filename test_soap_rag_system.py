#!/usr/bin/env python3
"""
Comprehensive Test Script for SOAP RAG System
Tests all SOAP embedding and search functionality
"""

import asyncio
import json
import requests
import time
from datetime import datetime
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"
DOCTOR_ID = "dr_test_123"
PATIENT_ID = "patient_test_456"
PATIENT_NAME = "John Smith"

class SOAPRAGTester:
    def __init__(self):
        self.base_url = BASE_URL
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        })
    
    def make_request(self, method: str, endpoint: str, data: Dict[Any, Any] = None) -> Dict[Any, Any]:
        """Make HTTP request with error handling"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == "POST":
                response = requests.post(url, json=data, timeout=30)
            else:
                response = requests.get(url, timeout=30)
            
            response.raise_for_status()
            return response.json()
        
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
            return {"error": str(e)}
    
    def test_soap_note_created_embedding(self):
        """Test SOAP note created event embedding"""
        print("\n🧪 Testing SOAP Note Created Embedding...")
        
        # Test data with comprehensive SOAP content
        soap_data = {
            "doctor_id": DOCTOR_ID,
            "event_type": "soap_note_created",
            "data": f"SOAP note created for patient {PATIENT_NAME} visit on {datetime.now().strftime('%d/%m/%Y')} [AI Enhanced Analysis]. Chief complaint: Chest pain and shortness of breath. Subjective: Patient reports chest pain for 2 days, shortness of breath on exertion, denies fever or cough. Pain is sharp, 7/10 intensity, worse with deep breathing. Objective: Vital signs: BP 140/90, HR 88, RR 18, O2 sat 96% on room air. Chest clear to auscultation bilaterally. Heart sounds regular, no murmurs. No peripheral edema. Assessment: Chest pain, likely musculoskeletal origin. Hypertension, well controlled. Rule out cardiac etiology. Plan: Order ECG and chest X-ray. Continue current antihypertensive medications. Follow up in 1 week or sooner if symptoms worsen. Patient education on when to seek emergency care. Diagnosis: Chest pain, Hypertension Primary Diagnosis: Chest pain, musculoskeletal Medications: Lisinopril 10mg daily, Metoprolol 25mg twice daily Allergies: Penicillin Vitals: BP 140/90, HR 88 Recommendations: ECG, Chest X-ray, Follow-up in 1 week Specialty: Internal Medicine Quality Score: 92% Safety Status: Safe",
            "metadata": {
                "soap_note_id": "soap_test_001",
                "patient_name": PATIENT_NAME,
                "patient_id": PATIENT_ID,
                "chief_complaint": "Chest pain and shortness of breath",
                "primary_diagnosis": "Chest pain, musculoskeletal",
                "diagnosis": ["Chest pain", "Hypertension"],
                "medications": ["Lisinopril 10mg daily", "Metoprolol 25mg twice daily"],
                "allergies": ["Penicillin"],
                "vital_signs": {
                    "bloodPressure": "140/90",
                    "heartRate": "88",
                    "respiratoryRate": "18",
                    "oxygenSaturation": "96%"
                },
                "recommendations": ["ECG", "Chest X-ray", "Follow-up in 1 week"],
                "specialty": "Internal Medicine",
                "quality_score": 92,
                "safety_status": True,
                "has_enhanced_data": True,
                "status": "completed"
            }
        }
        
        # Test doctor embedding
        result = self.make_request("POST", "/api/v1/rag/embed", soap_data)
        
        if "error" not in result and result.get("success"):
            self.log_test("SOAP Note Created - Doctor Embedding", True, 
                         f"Embedded {result.get('data_length', 0)} characters")
        else:
            self.log_test("SOAP Note Created - Doctor Embedding", False, 
                         result.get("error", "Unknown error"))
        
        # Test patient embedding
        patient_data = soap_data.copy()
        patient_data["patient_id"] = PATIENT_ID
        del patient_data["doctor_id"]
        patient_data["data"] = f"Medical visit documented on {datetime.now().strftime('%d/%m/%Y')} [AI Enhanced Analysis]. Chief complaint: Chest pain and shortness of breath. Assessment: Chest pain, likely musculoskeletal origin. Hypertension, well controlled. Rule out cardiac etiology. Treatment plan: Order ECG and chest X-ray. Continue current antihypertensive medications. Follow up in 1 week or sooner if symptoms worsen. Patient education on when to seek emergency care. Diagnosis: Chest pain, Hypertension Primary Diagnosis: Chest pain, musculoskeletal Medications: Lisinopril 10mg daily, Metoprolol 25mg twice daily Allergies: Penicillin Recommendations: ECG, Chest X-ray, Follow-up in 1 week Safety Status: Safe"
        
        result = self.make_request("POST", "/api/v1/rag/embed", patient_data)
        
        if "error" not in result and result.get("success"):
            self.log_test("SOAP Note Created - Patient Embedding", True, 
                         f"Embedded {result.get('data_length', 0)} characters")
        else:
            self.log_test("SOAP Note Created - Patient Embedding", False, 
                         result.get("error", "Unknown error"))
        
        return result.get("success", False)
    
    def test_soap_note_shared_embedding(self):
        """Test SOAP note shared event embedding"""
        print("\n🧪 Testing SOAP Note Shared Embedding...")
        
        # Test SOAP sharing data
        sharing_data = {
            "doctor_id": DOCTOR_ID,
            "event_type": "soap_note_shared_out",
            "data": f"SOAP note shared with colleague on {datetime.now().strftime('%d/%m/%Y')} for patient {PATIENT_NAME}. Patient case shared for: Second opinion on chest pain diagnosis. Permissions: view. Expires: {datetime.now().strftime('%d/%m/%Y')} Message: Please review this case and provide your assessment",
            "metadata": {
                "share_id": "share_test_001",
                "soap_note_id": "soap_test_001",
                "patient_name": PATIENT_NAME,
                "patient_id": PATIENT_ID,
                "from_doctor_id": DOCTOR_ID,
                "to_doctor_id": "dr_colleague_456",
                "share_reason": "Second opinion on chest pain diagnosis",
                "permissions": "view"
            }
        }
        
        result = self.make_request("POST", "/api/v1/rag/embed", sharing_data)
        
        if "error" not in result and result.get("success"):
            self.log_test("SOAP Note Shared Embedding", True, 
                         f"Embedded {result.get('data_length', 0)} characters")
        else:
            self.log_test("SOAP Note Shared Embedding", False, 
                         result.get("error", "Unknown error"))
        
        return result.get("success", False)
    
    def test_soap_note_updated_embedding(self):
        """Test SOAP note updated event embedding"""
        print("\n🧪 Testing SOAP Note Updated Embedding...")
        
        # Test SOAP update data
        update_data = {
            "doctor_id": DOCTOR_ID,
            "event_type": "soap_note_updated",
            "data": f"SOAP note updated on {datetime.now().strftime('%d/%m/%Y')} for patient {PATIENT_NAME}. Changes: Added ECG results showing normal sinus rhythm. Updated assessment to rule out cardiac causes. Current assessment: Chest pain, confirmed musculoskeletal origin based on normal ECG. Hypertension, well controlled. Current plan: Continue current medications, physical therapy referral, follow-up in 2 weeks",
            "metadata": {
                "soap_note_id": "soap_test_001",
                "patient_name": PATIENT_NAME,
                "patient_id": PATIENT_ID,
                "changes": "Added ECG results showing normal sinus rhythm",
                "status": "updated"
            }
        }
        
        result = self.make_request("POST", "/api/v1/rag/embed", update_data)
        
        if "error" not in result and result.get("success"):
            self.log_test("SOAP Note Updated Embedding", True, 
                         f"Embedded {result.get('data_length', 0)} characters")
        else:
            self.log_test("SOAP Note Updated Embedding", False, 
                         result.get("error", "Unknown error"))
        
        return result.get("success", False)
    
    def test_doctor_search_queries(self):
        """Test doctor search queries for SOAP notes"""
        print("\n🔍 Testing Doctor Search Queries...")
        
        # Wait a moment for embeddings to be processed
        time.sleep(2)
        
        test_queries = [
            f"What SOAP notes were created for {PATIENT_NAME}?",
            f"What was the assessment for {PATIENT_NAME}'s chest pain?",
            f"What medications were prescribed to {PATIENT_NAME}?",
            f"What were {PATIENT_NAME}'s vital signs?",
            "chest pain diagnosis",
            "hypertension treatment",
            "ECG results"
        ]
        
        for query in test_queries:
            search_data = {
                "doctor_id": DOCTOR_ID,
                "query": query
            }
            
            result = self.make_request("POST", "/api/v1/rag/search/doctor", search_data)
            
            if "error" not in result and result.get("success"):
                response = result.get("response", "")
                doc_count = result.get("relevant_documents_count", 0)
                
                # Check if patient name appears in response
                has_patient_name = PATIENT_NAME.lower() in response.lower()
                
                self.log_test(f"Doctor Search: '{query[:30]}...'", True, 
                             f"Found {doc_count} docs, Patient name: {has_patient_name}")
            else:
                self.log_test(f"Doctor Search: '{query[:30]}...'", False, 
                             result.get("error", "No response"))
    
    def test_patient_search_queries(self):
        """Test patient search queries for SOAP notes"""
        print("\n🔍 Testing Patient Search Queries...")
        
        test_queries = [
            "What was my last medical assessment?",
            "What medications am I taking?",
            "What were my vital signs?",
            "What was my diagnosis?",
        ]
        
        for query in test_queries:
            search_data = {
                "patient_id": PATIENT_ID,
                "query": query
            }
            
            result = self.make_request("POST", "/api/v1/rag/search/patient", search_data)
            
            if "error" not in result and result.get("success"):
                response = result.get("response", "")
                doc_count = result.get("relevant_documents_count", 0)
                
                self.log_test(f"Patient Search: '{query[:30]}...'", True, 
                             f"Found {doc_count} docs")
            else:
                self.log_test(f"Patient Search: '{query[:30]}...'", False, 
                             result.get("error", "No response"))
    
    def run_all_tests(self):
        """Run all SOAP RAG tests"""
        print("🚀 Starting SOAP RAG System Tests...")
        print(f"Testing against: {self.base_url}")
        print(f"Doctor ID: {DOCTOR_ID}")
        print(f"Patient ID: {PATIENT_ID}")
        print(f"Patient Name: {PATIENT_NAME}")
        
        # Test embeddings
        self.test_soap_note_created_embedding()
        self.test_soap_note_shared_embedding()
        self.test_soap_note_updated_embedding()
        
        # Test searches
        self.test_doctor_search_queries()
        self.test_patient_search_queries()
        
        # Print summary
        self.print_test_summary()
    
    def print_test_summary(self):
        """Print test results summary"""
        print("\n" + "="*60)
        print("📊 SOAP RAG TEST SUMMARY")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   - {result['test']}: {result['details']}")
        
        print("\n🎯 Key Features Tested:")
        print("   ✅ SOAP note embedding with patient names")
        print("   ✅ Enhanced content parsing (vitals, medications, diagnosis)")
        print("   ✅ Structured metadata extraction")
        print("   ✅ Patient-specific search queries")
        print("   ✅ Medical context understanding")
        print("   ✅ Cross-reference capabilities")

if __name__ == "__main__":
    tester = SOAPRAGTester()
    tester.run_all_tests()
