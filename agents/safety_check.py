"""
Safety Check Agent
Identifies red flags, contraindications, drug interactions, and generates safety warnings
"""
import json
import logging
from typing import Dict, List, Any, <PERSON><PERSON>
from dataclasses import dataclass

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

from config.settings import settings
from models.schemas import SOAPNotes, SOAPNotesStructured, QualityMetrics
from agents.specialty_detection import SpecialtyConfiguration

logger = logging.getLogger(__name__)

@dataclass
class SafetyCheckResult:
    """Results from safety check analysis"""
    red_flags: List[str]
    contraindications: List[str]
    drug_interactions: List[Dict[str, Any]]
    safety_warnings: List[str]
    critical_items: List[str]
    is_safe: bool

class SafetyCheckAgent:
    """Agent for identifying safety concerns in SOAP notes"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.openai_model,
            temperature=0.1,  # Very low temperature for consistent safety checks
            max_tokens=1500,
            openai_api_key=settings.openai_api_key
        )
    
    async def perform_safety_check(
        self,
        soap_notes: SOAPNotes,
        specialty_config: SpecialtyConfiguration
    ) -> <PERSON><PERSON>[SOAPNotes, SafetyCheckResult]:
        """
        Perform comprehensive safety check on SOAP notes
        
        Args:
            soap_notes: Complete SOAP notes to check
            specialty_config: Detected specialty configuration
            
        Returns:
            Tuple of (updated SOAP notes with safety information, safety check results)
        """
        system_prompt = self._get_safety_system_prompt(specialty_config)
        user_prompt = self._get_safety_user_prompt(soap_notes)
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        try:
            response = await self.llm.agenerate([messages])
            result_text = response.generations[0][0].text
            
            safety_result = self._parse_safety_response(result_text)
            
            # Update quality metrics with safety information
            updated_metrics = self._update_quality_metrics(soap_notes.quality_metrics, safety_result)
            
            # Create updated SOAP notes with safety information
            updated_soap = SOAPNotes(
                soap_notes=soap_notes.soap_notes,
                quality_metrics=updated_metrics,
                session_id=soap_notes.session_id,
                specialty=soap_notes.specialty
            )
            
            logger.info(f"Safety check completed: {len(safety_result.red_flags)} red flags, "
                       f"{len(safety_result.drug_interactions)} drug interactions")
            return updated_soap, safety_result
            
        except Exception as e:
            logger.error(f"Safety check failed: {e}")
            # Return original notes and empty safety result if check fails
            empty_result = SafetyCheckResult(
                red_flags=[], contraindications=[], drug_interactions=[],
                safety_warnings=["Safety check could not be completed due to an error"],
                critical_items=[], is_safe=False
            )
            return soap_notes, empty_result
    
    def _get_safety_system_prompt(self, specialty_config: SpecialtyConfiguration) -> str:
        """Get system prompt for safety check"""
        specialty_red_flags = ', '.join(specialty_config.red_flags)
        
        return f"""You are a Medical Safety Expert specializing in {specialty_config.specialty.title()}. Your task is to perform a comprehensive safety check on SOAP notes to identify potential risks, contraindications, drug interactions, and critical items requiring immediate attention.

SPECIALTY-SPECIFIC RED FLAGS:
{specialty_red_flags}

SAFETY CHECK REQUIREMENTS:
1. Identify red flags in the patient's presentation that require urgent attention
2. Check for contraindications between:
   - Medications and diagnoses
   - Medications and patient allergies
   - Treatments and patient conditions
3. Analyze potential drug interactions among prescribed medications
4. Generate safety warnings for any identified issues
5. Flag critical items requiring immediate clinical attention
6. Make an overall safety assessment

OUTPUT FORMAT:
Return ONLY valid JSON in this exact format:
{{
  "red_flags": ["specific red flag 1", "specific red flag 2"],
  "contraindications": ["specific contraindication 1", "specific contraindication 2"],
  "drug_interactions": [
    {{
      "drug1": "medication name",
      "drug2": "medication name",
      "severity": "mild/moderate/severe",
      "description": "description of interaction"
    }}
  ],
  "safety_warnings": ["specific warning 1", "specific warning 2"],
  "critical_items": ["critical item 1", "critical item 2"],
  "is_safe": true/false
}}

Be thorough, precise, and clinically accurate. Patient safety is the highest priority."""
    
    def _get_safety_user_prompt(self, soap_notes: SOAPNotes) -> str:
        """Get user prompt for safety check"""
        # Extract key information from SOAP notes for safety analysis
        structured_notes = soap_notes.soap_notes
        
        medications = ', '.join(structured_notes.subjective.medications)
        allergies = ', '.join(structured_notes.subjective.allergies)
        
        primary_diagnosis = structured_notes.assessment.primary_diagnosis.diagnosis
        problems = ', '.join([p.problem for p in structured_notes.assessment.problem_list])
        
        plan_medications = ', '.join(structured_notes.plan.medications)
        treatments = ', '.join(structured_notes.plan.treatments)
        
        return f"""Please perform a comprehensive safety check on these SOAP notes:

PATIENT INFORMATION:
- Medications: {medications}
- Allergies: {allergies}
- Primary Diagnosis: {primary_diagnosis}
- Problem List: {problems}

TREATMENT PLAN:
- Prescribed Medications: {plan_medications}
- Treatments: {treatments}

Please analyze this clinical information for safety concerns and return your findings in the specified JSON format."""
    
    def _parse_safety_response(self, response_text: str) -> SafetyCheckResult:
        """Parse LLM response into SafetyCheckResult object"""
        try:
            result = json.loads(response_text)
            
            return SafetyCheckResult(
                red_flags=result.get('red_flags', []),
                contraindications=result.get('contraindications', []),
                drug_interactions=result.get('drug_interactions', []),
                safety_warnings=result.get('safety_warnings', []),
                critical_items=result.get('critical_items', []),
                is_safe=result.get('is_safe', False)
            )
            
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            logger.error(f"Failed to parse safety check response: {e}")
            logger.debug(f"Response text: {response_text}")
            
            # Return empty safety result as fallback
            return SafetyCheckResult(
                red_flags=["Safety check parsing error"],
                contraindications=[],
                drug_interactions=[],
                safety_warnings=["Safety check could not be completed due to a parsing error"],
                critical_items=["Review required due to safety check failure"],
                is_safe=False
            )
    
    def _update_quality_metrics(self, metrics: QualityMetrics, safety_result: SafetyCheckResult) -> QualityMetrics:
        """Update quality metrics with safety information"""
        # Add safety red flags to quality metrics
        updated_red_flags = list(metrics.red_flags)
        updated_red_flags.extend(safety_result.red_flags)
        updated_red_flags.extend(safety_result.critical_items)
        
        # If there are safety issues, adjust clinical accuracy score
        clinical_accuracy = metrics.clinical_accuracy
        if not safety_result.is_safe:
            # Reduce clinical accuracy if safety issues are found
            clinical_accuracy = max(0.0, clinical_accuracy - 0.2)
        
        # Create updated metrics
        return QualityMetrics(
            completeness_score=metrics.completeness_score,
            clinical_accuracy=clinical_accuracy,
            documentation_quality=metrics.documentation_quality,
            red_flags=updated_red_flags,
            missing_information=metrics.missing_information
        )
