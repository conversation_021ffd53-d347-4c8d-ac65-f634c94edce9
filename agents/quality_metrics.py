"""
Quality Metrics Agent
Calculates completeness scores, clinical accuracy, documentation quality, and identifies missing information
"""
import json
import logging
from typing import Dict, List, Any
from dataclasses import dataclass

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

from config.settings import settings
from models.schemas import SOAPNotesStructured, QualityMetrics
from agents.specialty_detection import SpecialtyConfiguration

logger = logging.getLogger(__name__)

@dataclass
class QualityAnalysis:
    """Detailed quality analysis results"""
    completeness_score: float
    clinical_accuracy: float
    documentation_quality: float
    missing_information: List[str]
    quality_issues: List[str]
    recommendations: List[str]

class QualityMetricsAgent:
    """Agent for calculating quality metrics and identifying documentation gaps"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.openai_model,
            temperature=0.1,  # Low temperature for consistent quality assessment
            max_tokens=1500,
            openai_api_key=settings.openai_api_key
        )
    
    async def calculate_quality_metrics(
        self,
        soap_notes: SOAPNotesStructured,
        transcription: str,
        specialty_config: SpecialtyConfiguration
    ) -> QualityMetrics:
        """
        Calculate comprehensive quality metrics for SOAP notes
        
        Args:
            soap_notes: Structured SOAP notes to evaluate
            transcription: Original transcription for completeness check
            specialty_config: Detected specialty configuration
            
        Returns:
            QualityMetrics with calculated scores and identified issues
        """
        system_prompt = self._get_quality_system_prompt(specialty_config)
        user_prompt = self._get_quality_user_prompt(soap_notes, transcription)
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        try:
            response = await self.llm.agenerate([messages])
            result_text = response.generations[0][0].text
            
            quality_analysis = self._parse_quality_response(result_text)
            
            # Create quality metrics object
            quality_metrics = QualityMetrics(
                completeness_score=quality_analysis.completeness_score,
                clinical_accuracy=quality_analysis.clinical_accuracy,
                documentation_quality=quality_analysis.documentation_quality,
                red_flags=[],  # Will be populated by safety check agent
                missing_information=quality_analysis.missing_information
            )
            
            logger.info(f"Quality metrics calculated: "
                       f"Completeness: {quality_analysis.completeness_score:.2f}, "
                       f"Accuracy: {quality_analysis.clinical_accuracy:.2f}, "
                       f"Documentation: {quality_analysis.documentation_quality:.2f}")
            return quality_metrics
            
        except Exception as e:
            logger.error(f"Quality metrics calculation failed: {e}")
            # Return default metrics if calculation fails
            return self._get_fallback_metrics()
    
    def _get_quality_system_prompt(self, specialty_config: SpecialtyConfiguration) -> str:
        """Get system prompt for quality metrics calculation"""
        required_sections = ', '.join(specialty_config.required_sections)
        quality_indicators = ', '.join(specialty_config.quality_indicators)
        
        return f"""You are a Medical Documentation Quality Expert specializing in {specialty_config.specialty.title()}. Your task is to evaluate SOAP notes for completeness, clinical accuracy, and documentation quality.

SPECIALTY-SPECIFIC REQUIREMENTS:
- Required Sections: {required_sections}
- Quality Indicators: {quality_indicators}
- Focus Areas: {', '.join(specialty_config.focus_areas)}

EVALUATION CRITERIA:

1. COMPLETENESS SCORE (0.0-1.0):
   - All required sections present and adequately detailed
   - Specialty-specific elements included
   - No critical information gaps
   - Comprehensive patient history and examination

2. CLINICAL ACCURACY (0.0-1.0):
   - Diagnoses supported by clinical evidence
   - Appropriate ICD-10 codes
   - Logical clinical reasoning
   - Evidence-based treatment plans
   - Proper medication dosing and selection

3. DOCUMENTATION QUALITY (0.0-1.0):
   - Clear, professional language
   - Proper medical terminology
   - Logical organization and flow
   - Adequate detail for clinical decision-making
   - Compliance with documentation standards

OUTPUT FORMAT:
Return ONLY valid JSON in this exact format:
{{
  "completeness_score": 0.0-1.0,
  "clinical_accuracy": 0.0-1.0,
  "documentation_quality": 0.0-1.0,
  "missing_information": ["specific missing item 1", "specific missing item 2"],
  "quality_issues": ["specific issue 1", "specific issue 2"],
  "recommendations": ["specific recommendation 1", "specific recommendation 2"]
}}

Be thorough, objective, and clinically accurate in your assessment."""
    
    def _get_quality_user_prompt(self, soap_notes: SOAPNotesStructured, transcription: str) -> str:
        """Get user prompt for quality metrics calculation"""
        # Create a comprehensive summary of the SOAP notes for evaluation
        subjective_summary = f"""
Chief Complaint: {soap_notes.subjective.chief_complaint}
HPI: {soap_notes.subjective.history_present_illness}
ROS: {', '.join(soap_notes.subjective.review_of_systems)}
PMH: {', '.join(soap_notes.subjective.past_medical_history)}
Medications: {', '.join(soap_notes.subjective.medications)}
Allergies: {', '.join(soap_notes.subjective.allergies)}
Social History: {soap_notes.subjective.social_history}
"""
        
        objective_summary = f"""
Vital Signs: {soap_notes.objective.vital_signs}
Physical Exam: {soap_notes.objective.physical_exam}
Diagnostic Results: {', '.join(soap_notes.objective.diagnostic_results)}
Mental Status: {soap_notes.objective.mental_status}
Functional Status: {soap_notes.objective.functional_status}
"""
        
        assessment_summary = f"""
Primary Diagnosis: {soap_notes.assessment.primary_diagnosis.diagnosis} ({soap_notes.assessment.primary_diagnosis.icd10_code})
Confidence: {soap_notes.assessment.primary_diagnosis.confidence:.2f}
Clinical Reasoning: {soap_notes.assessment.primary_diagnosis.clinical_reasoning}
Differential Diagnoses: {len(soap_notes.assessment.differential_diagnoses)} listed
Problem List: {len(soap_notes.assessment.problem_list)} problems
Risk Level: {soap_notes.assessment.risk_level}
"""
        
        plan_summary = f"""
Diagnostic Workup: {', '.join(soap_notes.plan.diagnostic_workup)}
Treatments: {', '.join(soap_notes.plan.treatments)}
Medications: {', '.join(soap_notes.plan.medications)}
Follow-up: {len(soap_notes.plan.follow_up)} appointments
Patient Education: {', '.join(soap_notes.plan.patient_education)}
Referrals: {', '.join(soap_notes.plan.referrals)}
"""
        
        return f"""Please evaluate the quality of these SOAP notes:

SUBJECTIVE SECTION:
{subjective_summary}

OBJECTIVE SECTION:
{objective_summary}

ASSESSMENT SECTION:
{assessment_summary}

PLAN SECTION:
{plan_summary}

CLINICAL NOTES:
{soap_notes.clinical_notes}

ORIGINAL TRANSCRIPTION (for completeness comparison):
{transcription[:1000]}...

Please analyze this documentation and return quality metrics in the specified JSON format."""
    
    def _parse_quality_response(self, response_text: str) -> QualityAnalysis:
        """Parse LLM response into QualityAnalysis object"""
        try:
            result = json.loads(response_text)
            
            return QualityAnalysis(
                completeness_score=float(result.get('completeness_score', 0.5)),
                clinical_accuracy=float(result.get('clinical_accuracy', 0.5)),
                documentation_quality=float(result.get('documentation_quality', 0.5)),
                missing_information=result.get('missing_information', []),
                quality_issues=result.get('quality_issues', []),
                recommendations=result.get('recommendations', [])
            )
            
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            logger.error(f"Failed to parse quality metrics response: {e}")
            logger.debug(f"Response text: {response_text}")
            
            # Return default analysis as fallback
            return QualityAnalysis(
                completeness_score=0.5,
                clinical_accuracy=0.5,
                documentation_quality=0.5,
                missing_information=["Quality assessment could not be completed"],
                quality_issues=["Quality metrics parsing error"],
                recommendations=["Manual review required due to quality assessment failure"]
            )
    
    def _get_fallback_metrics(self) -> QualityMetrics:
        """Get fallback quality metrics when calculation fails"""
        return QualityMetrics(
            completeness_score=0.5,
            clinical_accuracy=0.5,
            documentation_quality=0.5,
            red_flags=["Quality metrics calculation failed"],
            missing_information=["Quality assessment could not be completed due to an error"]
        )
