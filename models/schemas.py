"""
Pydantic models and schemas for API requests/responses
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from dataclasses import dataclass, asdict
import re
import json

# API Request Models
class RecordingRequest(BaseModel):
    """Request to start a recording session"""
    doctor_id: Optional[str] = Field(default="default_doctor", description="Doctor identifier")
    patient_id: Optional[str] = Field(default="default_patient", description="Patient identifier")
    specialty: Optional[str] = Field(default="general", description="Medical specialty")
    action: str = Field(default="start_recording", description="Action to perform")

class VoiceCommandRequest(BaseModel):
    """Request to process a voice command"""
    command: str = Field(..., description="Voice command text")
    session_id: str = Field(..., description="Recording session ID")

class TranscriptionRequest(BaseModel):
    """Request for transcription processing"""
    session_id: str = Field(..., description="Recording session ID")
    audio_duration: Optional[float] = Field(None, description="Audio duration in seconds")

class SimpleVoiceRequest(BaseModel):
    """Simplified request for voice processing - no session management needed"""
    patient_name: str = Field(..., description="Patient full name")
    patient_id: Optional[str] = Field(None, description="Patient identifier (optional)")
    doctor_name: str = Field(..., description="Doctor full name")
    doctor_id: Optional[str] = Field(None, description="Doctor identifier (optional)")
    specialty: str = Field(default="general", description="Medical specialty")
    appointment_date: Optional[str] = Field(None, description="Appointment date (YYYY-MM-DD)")
    chief_complaint: Optional[str] = Field(None, description="Chief complaint or reason for visit")

# Data Classes for Internal Use
@dataclass
class SessionData:
    """Recording session data"""
    session_id: str
    start_time: datetime
    status: str
    doctor_id: str
    patient_id: str
    specialty: str
    audio_chunks: List[str]
    
    def to_dict(self):
        return {
            **asdict(self),
            'start_time': self.start_time.isoformat(),
            'audio_chunks': str(self.audio_chunks)  # Convert to JSON string
        }

@dataclass
class TranscriptionResult:
    """Result of audio transcription"""
    text: str
    confidence: float
    language: str
    duration: float

@dataclass
class ValidationResult:
    """Result of medical terminology validation"""
    validated_text: str
    corrections: List[Dict[str, Any]]
    flags: List[Dict[str, Any]]
    confidence: float

# Enhanced SOAP Notes Structure
@dataclass
class SubjectiveSection:
    """Detailed subjective section of SOAP notes"""
    chief_complaint: str
    history_present_illness: str
    review_of_systems: List[str]
    past_medical_history: List[str]
    medications: List[str]
    allergies: List[str]
    social_history: str

@dataclass
class ObjectiveSection:
    """Detailed objective section of SOAP notes"""
    vital_signs: Dict[str, Any]
    physical_exam: Dict[str, Any]
    diagnostic_results: List[str]
    mental_status: str
    functional_status: str

@dataclass
class PrimaryDiagnosis:
    """Primary diagnosis with clinical reasoning"""
    diagnosis: str
    icd10_code: str
    confidence: float
    severity: str  # mild/moderate/severe
    clinical_reasoning: str

@dataclass
class DifferentialDiagnosis:
    """Differential diagnosis entry"""
    diagnosis: str
    icd10_code: str
    probability: float
    ruling_out_criteria: str

@dataclass
class ProblemListItem:
    """Problem list item"""
    problem: str
    status: str  # active/resolved/chronic
    priority: str  # high/medium/low

@dataclass
class AssessmentSection:
    """Detailed assessment section of SOAP notes"""
    primary_diagnosis: PrimaryDiagnosis
    differential_diagnoses: List[DifferentialDiagnosis]
    problem_list: List[ProblemListItem]
    risk_level: str  # low/moderate/high
    risk_factors: List[str]
    prognosis: str

@dataclass
class FollowUpItem:
    """Follow-up appointment details"""
    provider: str
    timeframe: str
    urgency: str  # routine/urgent/stat

@dataclass
class AppointmentDetails:
    """Structured appointment information for embedding"""
    appointment_id: Optional[str] = None
    patient_name: Optional[str] = None
    patient_id: Optional[str] = None
    doctor_name: Optional[str] = None
    doctor_id: Optional[str] = None
    appointment_date: Optional[str] = None  # YYYY-MM-DD
    appointment_time: Optional[str] = None  # HH:MM
    appointment_type: Optional[str] = None  # follow_up, routine, urgent, etc.
    location: Optional[str] = None  # in_person, telehealth, clinic_room_1, etc.
    duration_minutes: Optional[int] = None
    reason: Optional[str] = None
    notes: Optional[str] = None
    status: Optional[str] = None  # scheduled, confirmed, cancelled, completed
    special_instructions: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for metadata storage"""
        return {k: v for k, v in asdict(self).items() if v is not None}

    @classmethod
    def parse_from_text(cls, text: str, role_type: str = None) -> 'AppointmentDetails':
        """Parse appointment details from text content"""
        details = cls()

        # Extract patient name
        patient_match = re.search(r'(?:patient|with)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)', text, re.IGNORECASE)
        if patient_match:
            details.patient_name = patient_match.group(1).strip()

        # Extract date (various formats)
        date_patterns = [
            r'(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})',  # DD/MM/YYYY or DD-MM-YYYY
            r'(\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})',  # YYYY/MM/DD or YYYY-MM-DD
            r'(\d{1,2}(?:st|nd|rd|th)?\s+(?:of\s+)?(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{4})',
        ]
        for pattern in date_patterns:
            date_match = re.search(pattern, text, re.IGNORECASE)
            if date_match:
                details.appointment_date = date_match.group(1)
                break

        # Extract time
        time_match = re.search(r'(\d{1,2}:\d{2}(?::\d{2})?(?:\s*[AP]M)?)', text, re.IGNORECASE)
        if time_match:
            details.appointment_time = time_match.group(1)

        # Extract appointment type
        type_keywords = ['follow_up', 'follow-up', 'routine', 'urgent', 'emergency', 'consultation', 'checkup', 'check-up']
        for keyword in type_keywords:
            if keyword.replace('_', '').replace('-', '') in text.lower().replace('_', '').replace('-', ''):
                details.appointment_type = keyword.replace('-', '_')
                break

        # Extract location
        location_keywords = ['in_person', 'in-person', 'telehealth', 'virtual', 'clinic', 'room', 'office']
        for keyword in location_keywords:
            if keyword.replace('_', '').replace('-', '') in text.lower():
                details.location = keyword.replace('-', '_')
                break

        # Extract reason/notes
        reason_patterns = [
            r'(?:for|reason:?)\s+([^.]+)',
            r'(?:regarding|about)\s+([^.]+)',
            r'(?:due to|because of)\s+([^.]+)'
        ]
        for pattern in reason_patterns:
            reason_match = re.search(pattern, text, re.IGNORECASE)
            if reason_match:
                details.reason = reason_match.group(1).strip()
                break

        # Extract special instructions
        instruction_patterns = [
            r'(?:bring|remember to bring|please bring)\s+([^.]+)',
            r'(?:notes?:?)\s+([^.]+)',
            r'(?:instructions?:?)\s+([^.]+)'
        ]
        for pattern in instruction_patterns:
            instruction_match = re.search(pattern, text, re.IGNORECASE)
            if instruction_match:
                details.special_instructions = instruction_match.group(1).strip()
                break

        # Set default status
        details.status = 'scheduled'

        return details

@dataclass
class PlanSection:
    """Detailed plan section of SOAP notes"""
    diagnostic_workup: List[str]
    treatments: List[str]
    medications: List[str]
    follow_up: List[FollowUpItem]
    patient_education: List[str]
    referrals: List[str]

@dataclass
class QualityMetrics:
    """Quality assessment metrics"""
    completeness_score: float
    clinical_accuracy: float
    documentation_quality: float
    red_flags: List[str]
    missing_information: List[str]

@dataclass
class SOAPNotesStructured:
    """Complete structured SOAP notes"""
    subjective: SubjectiveSection
    objective: ObjectiveSection
    assessment: AssessmentSection
    plan: PlanSection
    clinical_notes: str

@dataclass
class SOAPNotes:
    """Enhanced SOAP format clinical notes with quality metrics"""
    soap_notes: SOAPNotesStructured
    quality_metrics: QualityMetrics
    session_id: str
    specialty: str

    # Legacy compatibility methods
    @property
    def subjective(self) -> str:
        """Legacy compatibility - return formatted subjective text"""
        subj = self.soap_notes.subjective
        return f"Chief Complaint: {subj.chief_complaint}\n" \
               f"History of Present Illness: {subj.history_present_illness}\n" \
               f"Review of Systems: {', '.join(subj.review_of_systems)}\n" \
               f"Past Medical History: {', '.join(subj.past_medical_history)}\n" \
               f"Medications: {', '.join(subj.medications)}\n" \
               f"Allergies: {', '.join(subj.allergies)}\n" \
               f"Social History: {subj.social_history}"

    @property
    def objective(self) -> str:
        """Legacy compatibility - return formatted objective text"""
        obj = self.soap_notes.objective
        return f"Vital Signs: {obj.vital_signs}\n" \
               f"Physical Exam: {obj.physical_exam}\n" \
               f"Diagnostic Results: {', '.join(obj.diagnostic_results)}\n" \
               f"Mental Status: {obj.mental_status}\n" \
               f"Functional Status: {obj.functional_status}"

    @property
    def assessment(self) -> str:
        """Legacy compatibility - return formatted assessment text"""
        assess = self.soap_notes.assessment
        primary = assess.primary_diagnosis
        differentials = '\n'.join([f"- {d.diagnosis} ({d.probability:.1%})"
                                 for d in assess.differential_diagnoses])
        problems = '\n'.join([f"- {p.problem} ({p.status}, {p.priority} priority)"
                            for p in assess.problem_list])

        return f"Primary Diagnosis: {primary.diagnosis} ({primary.icd10_code}) - {primary.severity}\n" \
               f"Confidence: {primary.confidence:.1%}\n" \
               f"Clinical Reasoning: {primary.clinical_reasoning}\n\n" \
               f"Differential Diagnoses:\n{differentials}\n\n" \
               f"Problem List:\n{problems}\n\n" \
               f"Risk Level: {assess.risk_level}\n" \
               f"Risk Factors: {', '.join(assess.risk_factors)}\n" \
               f"Prognosis: {assess.prognosis}"

    @property
    def plan(self) -> str:
        """Legacy compatibility - return formatted plan text"""
        plan = self.soap_notes.plan
        follow_ups = '\n'.join([f"- {f.provider} ({f.timeframe}, {f.urgency})"
                               for f in plan.follow_up])

        return f"Diagnostic Workup: {', '.join(plan.diagnostic_workup)}\n" \
               f"Treatments: {', '.join(plan.treatments)}\n" \
               f"Medications: {', '.join(plan.medications)}\n" \
               f"Follow-up:\n{follow_ups}\n" \
               f"Patient Education: {', '.join(plan.patient_education)}\n" \
               f"Referrals: {', '.join(plan.referrals)}"

    @property
    def icd_codes(self) -> List[str]:
        """Legacy compatibility - return all ICD codes"""
        codes = [self.soap_notes.assessment.primary_diagnosis.icd10_code]
        codes.extend([d.icd10_code for d in self.soap_notes.assessment.differential_diagnoses])
        return [code for code in codes if code]

@dataclass
class QualityAssessment:
    """Quality assessment results"""
    quality_score: int
    errors: List[str]
    warnings: List[str]
    recommendations: List[str]
    critical_flags: List[Dict[str, Any]]
    approved: bool

# API Response Models
class SessionResponse(BaseModel):
    """Response for session operations"""
    session_id: str
    message: str
    status: str
    start_time: Optional[str] = None

class SessionStatusResponse(BaseModel):
    """Response for session status"""
    session_id: str
    status: str
    start_time: Optional[str] = None
    processed_at: Optional[str] = None
    clinical_notes: Optional[Dict[str, Any]] = None
    ehr_status: Optional[str] = None

class ClinicalNotesResponse(BaseModel):
    """Response for clinical notes"""
    session_id: str
    soap_notes: Dict[str, Any]
    quality_assessment: Dict[str, Any]
    highlighting: Dict[str, Any]
    transcription_data: Dict[str, Any]
    created_at: Optional[str] = None

class SessionListResponse(BaseModel):
    """Response for session list"""
    sessions: List[Dict[str, Any]]
    count: int

class VoiceCommandResponse(BaseModel):
    """Response for voice command processing"""
    success: bool
    command_result: Dict[str, Any]

class SimpleVoiceResponse(BaseModel):
    """Response for simplified voice processing"""
    success: bool
    processing_id: str = Field(..., description="Unique processing identifier")
    patient_name: str
    doctor_name: str
    specialty: str
    transcription: Dict[str, Any] = Field(..., description="Transcription results")
    clinical_notes: Dict[str, Any] = Field(..., description="Generated SOAP notes")
    quality_assessment: Dict[str, Any] = Field(..., description="Quality assessment results")
    processing_time_seconds: float
    created_at: str

class HealthResponse(BaseModel):
    """Response for health check"""
    status: str
    timestamp: str
    version: str
    services: Dict[str, str]

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str
    timestamp: str
    path: Optional[str] = None
