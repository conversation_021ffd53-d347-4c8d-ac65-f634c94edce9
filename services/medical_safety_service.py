"""
Medical Safety Validation Service
Critical safety checks for clinical documentation
"""
import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from models.schemas import SOAPNotes, ValidationResult, QualityAssessment

logger = logging.getLogger(__name__)

@dataclass
class SafetyFlag:
    """Medical safety flag"""
    severity: str  # 'critical', 'high', 'medium', 'low'
    category: str  # 'drug_interaction', 'dosage_error', 'contraindication', 'allergy'
    message: str
    recommendation: str
    requires_immediate_attention: bool = False

@dataclass
class SafetyReport:
    """Comprehensive medical safety report"""
    overall_safety_score: float  # 0-100
    critical_flags: List[SafetyFlag]
    high_priority_flags: List[SafetyFlag]
    medium_priority_flags: List[SafetyFlag]
    drug_interactions: List[Dict[str, Any]]
    dosage_errors: List[Dict[str, Any]]
    contraindications: List[Dict[str, Any]]
    requires_physician_review: bool
    safe_for_ehr_integration: bool

class MedicalSafetyValidator:
    """Medical-grade safety validation for clinical documentation"""
    
    def __init__(self):
        self.critical_medications = {
            'warfarin', 'heparin', 'insulin', 'digoxin', 'lithium',
            'methotrexate', 'phenytoin', 'theophylline'
        }
        
        self.dangerous_combinations = {
            ('warfarin', 'aspirin'): 'Increased bleeding risk',
            ('ace_inhibitor', 'potassium'): 'Hyperkalemia risk',
            ('metformin', 'contrast'): 'Lactic acidosis risk'
        }
        
        self.critical_symptoms = {
            'chest pain', 'shortness of breath', 'severe headache',
            'loss of consciousness', 'severe abdominal pain',
            'difficulty breathing', 'stroke symptoms'
        }
    
    async def validate_clinical_safety(self, soap_notes: SOAPNotes) -> SafetyReport:
        """
        Comprehensive medical safety validation
        
        Args:
            soap_notes: Clinical notes to validate
            
        Returns:
            SafetyReport with all safety findings
        """
        logger.info(f"Starting medical safety validation for {soap_notes.session_id}")
        
        try:
            # Extract medications and dosages
            medications = self._extract_medications(soap_notes.plan)
            
            # Run all safety checks
            drug_interactions = await self._check_drug_interactions(medications)
            dosage_errors = self._validate_dosages(medications, soap_notes.plan)
            contraindications = self._check_contraindications(soap_notes)
            critical_symptoms = self._detect_critical_symptoms(soap_notes)
            
            # Compile all flags
            all_flags = []
            all_flags.extend(self._create_drug_interaction_flags(drug_interactions))
            all_flags.extend(self._create_dosage_error_flags(dosage_errors))
            all_flags.extend(self._create_contraindication_flags(contraindications))
            all_flags.extend(self._create_critical_symptom_flags(critical_symptoms))
            
            # Categorize flags by severity
            critical_flags = [f for f in all_flags if f.severity == 'critical']
            high_priority_flags = [f for f in all_flags if f.severity == 'high']
            medium_priority_flags = [f for f in all_flags if f.severity == 'medium']
            
            # Calculate overall safety score
            safety_score = self._calculate_safety_score(all_flags)
            
            # Determine review requirements
            requires_physician_review = (
                len(critical_flags) > 0 or 
                len(high_priority_flags) > 2 or 
                safety_score < 70
            )
            
            safe_for_ehr = safety_score >= 80 and len(critical_flags) == 0
            
            report = SafetyReport(
                overall_safety_score=safety_score,
                critical_flags=critical_flags,
                high_priority_flags=high_priority_flags,
                medium_priority_flags=medium_priority_flags,
                drug_interactions=drug_interactions,
                dosage_errors=dosage_errors,
                contraindications=contraindications,
                requires_physician_review=requires_physician_review,
                safe_for_ehr_integration=safe_for_ehr
            )
            
            logger.info(f"Safety validation completed - Score: {safety_score}, Critical flags: {len(critical_flags)}")
            return report
            
        except Exception as e:
            logger.error(f"Medical safety validation failed: {e}")
            # Return conservative safety report on error
            return SafetyReport(
                overall_safety_score=0,
                critical_flags=[SafetyFlag(
                    severity='critical',
                    category='system_error',
                    message=f"Safety validation failed: {str(e)}",
                    recommendation="Manual physician review required immediately",
                    requires_immediate_attention=True
                )],
                high_priority_flags=[],
                medium_priority_flags=[],
                drug_interactions=[],
                dosage_errors=[],
                contraindications=[],
                requires_physician_review=True,
                safe_for_ehr_integration=False
            )
    
    def _extract_medications(self, plan_text: str) -> List[Dict[str, Any]]:
        """Extract medications and dosages from plan text"""
        medications = []
        
        # Common medication patterns
        med_patterns = [
            r'(\w+)\s+(\d+(?:\.\d+)?)\s*(mg|g|ml|units?)',  # medication dose unit
            r'(\w+)\s+(\d+(?:\.\d+)?)\s*(mg|g|ml|units?)\s+(daily|twice daily|bid|tid|qid)',  # with frequency
        ]
        
        for pattern in med_patterns:
            matches = re.finditer(pattern, plan_text, re.IGNORECASE)
            for match in matches:
                medications.append({
                    'name': match.group(1).lower(),
                    'dose': float(match.group(2)),
                    'unit': match.group(3).lower(),
                    'frequency': match.group(4).lower() if len(match.groups()) > 3 else 'unknown'
                })
        
        return medications
    
    async def _check_drug_interactions(self, medications: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Check for dangerous drug interactions"""
        interactions = []
        
        med_names = [med['name'] for med in medications]
        
        for combo, warning in self.dangerous_combinations.items():
            if all(any(med_name in med for med in med_names) for med_name in combo):
                interactions.append({
                    'medications': combo,
                    'warning': warning,
                    'severity': 'high'
                })
        
        return interactions
    
    def _validate_dosages(self, medications: List[Dict[str, Any]], plan_text: str) -> List[Dict[str, Any]]:
        """Validate medication dosages against safe ranges"""
        dosage_errors = []
        
        # Safe dosage ranges (simplified - in production, use comprehensive drug database)
        safe_ranges = {
            'metformin': {'min': 500, 'max': 2000, 'unit': 'mg'},
            'insulin': {'min': 1, 'max': 100, 'unit': 'units'},
            'warfarin': {'min': 1, 'max': 10, 'unit': 'mg'},
            'digoxin': {'min': 0.125, 'max': 0.5, 'unit': 'mg'}
        }
        
        for med in medications:
            med_name = med['name']
            if med_name in safe_ranges:
                safe_range = safe_ranges[med_name]
                if med['unit'] == safe_range['unit']:
                    if med['dose'] < safe_range['min'] or med['dose'] > safe_range['max']:
                        dosage_errors.append({
                            'medication': med_name,
                            'prescribed_dose': med['dose'],
                            'safe_range': f"{safe_range['min']}-{safe_range['max']} {safe_range['unit']}",
                            'severity': 'high' if med_name in self.critical_medications else 'medium'
                        })
        
        return dosage_errors
    
    def _check_contraindications(self, soap_notes: SOAPNotes) -> List[Dict[str, Any]]:
        """Check for medical contraindications"""
        contraindications = []
        
        # Extract allergies and conditions from subjective/objective
        text = f"{soap_notes.subjective} {soap_notes.objective}".lower()
        
        # Check for common contraindications
        if 'allergy' in text and 'penicillin' in text and 'amoxicillin' in soap_notes.plan.lower():
            contraindications.append({
                'issue': 'Penicillin allergy with penicillin-based antibiotic prescribed',
                'severity': 'critical'
            })
        
        if 'kidney disease' in text and 'metformin' in soap_notes.plan.lower():
            contraindications.append({
                'issue': 'Metformin prescribed with kidney disease',
                'severity': 'high'
            })
        
        return contraindications
    
    def _detect_critical_symptoms(self, soap_notes: SOAPNotes) -> List[str]:
        """Detect critical symptoms requiring immediate attention"""
        critical_found = []
        text = f"{soap_notes.subjective} {soap_notes.objective}".lower()
        
        for symptom in self.critical_symptoms:
            if symptom in text:
                critical_found.append(symptom)
        
        return critical_found
    
    def _create_drug_interaction_flags(self, interactions: List[Dict[str, Any]]) -> List[SafetyFlag]:
        """Create safety flags for drug interactions"""
        flags = []
        for interaction in interactions:
            flags.append(SafetyFlag(
                severity='critical' if 'bleeding' in interaction['warning'] else 'high',
                category='drug_interaction',
                message=f"Drug interaction: {' + '.join(interaction['medications'])} - {interaction['warning']}",
                recommendation="Review medication combination with pharmacist",
                requires_immediate_attention='bleeding' in interaction['warning']
            ))
        return flags
    
    def _create_dosage_error_flags(self, dosage_errors: List[Dict[str, Any]]) -> List[SafetyFlag]:
        """Create safety flags for dosage errors"""
        flags = []
        for error in dosage_errors:
            flags.append(SafetyFlag(
                severity=error['severity'],
                category='dosage_error',
                message=f"Dosage concern: {error['medication']} {error['prescribed_dose']} outside safe range {error['safe_range']}",
                recommendation="Verify dosage with prescribing guidelines",
                requires_immediate_attention=error['severity'] == 'critical'
            ))
        return flags
    
    def _create_contraindication_flags(self, contraindications: List[Dict[str, Any]]) -> List[SafetyFlag]:
        """Create safety flags for contraindications"""
        flags = []
        for contra in contraindications:
            flags.append(SafetyFlag(
                severity=contra['severity'],
                category='contraindication',
                message=contra['issue'],
                recommendation="Review patient history and medication selection",
                requires_immediate_attention=contra['severity'] == 'critical'
            ))
        return flags
    
    def _create_critical_symptom_flags(self, symptoms: List[str]) -> List[SafetyFlag]:
        """Create safety flags for critical symptoms"""
        flags = []
        for symptom in symptoms:
            flags.append(SafetyFlag(
                severity='critical',
                category='critical_symptom',
                message=f"Critical symptom identified: {symptom}",
                recommendation="Immediate medical evaluation required",
                requires_immediate_attention=True
            ))
        return flags
    
    def _calculate_safety_score(self, flags: List[SafetyFlag]) -> float:
        """Calculate overall safety score"""
        if not flags:
            return 100.0
        
        # Deduct points based on flag severity
        score = 100.0
        for flag in flags:
            if flag.severity == 'critical':
                score -= 30
            elif flag.severity == 'high':
                score -= 15
            elif flag.severity == 'medium':
                score -= 5
        
        return max(0.0, score)
