"""
Clinical Document Generator Agent
"""
import logging
import os
import tempfile
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from io import BytesIO

from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT

from models.schemas import SOAPNotes, QualityAssessment
from config.settings import settings

logger = logging.getLogger(__name__)

class ClinicalDocumentGenerator:
    """Agent for generating formatted clinical documents"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Setup custom styles for clinical documents"""
        # Header style
        self.styles.add(ParagraphStyle(
            name='Header',
            parent=self.styles['Heading1'],
            fontSize=16,
            spaceAfter=12,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        ))
        
        # Section header style
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading2'],
            fontSize=12,
            spaceBefore=12,
            spaceAfter=6,
            textColor=colors.darkblue,
            borderWidth=1,
            borderColor=colors.darkblue,
            borderPadding=3
        ))
        
        # Clinical content style
        self.styles.add(ParagraphStyle(
            name='Clinical',
            parent=self.styles['Normal'],
            fontSize=10,
            spaceAfter=6,
            leftIndent=12
        ))
        
        # Footer style
        self.styles.add(ParagraphStyle(
            name='Footer',
            parent=self.styles['Normal'],
            fontSize=8,
            alignment=TA_CENTER,
            textColor=colors.grey
        ))
    
    async def generate_clinical_document(
        self,
        soap_notes: SOAPNotes,
        qa_results: QualityAssessment,
        patient_name: str,
        doctor_name: str,
        session_id: str,
        highlighting_data: Optional[Dict[str, Any]] = None,
        transcription_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate a formatted clinical document
        
        Args:
            soap_notes: SOAP notes content
            qa_results: Quality assessment results
            patient_name: Patient full name
            doctor_name: Doctor full name
            session_id: Session identifier
            highlighting_data: Smart highlighting results
            transcription_data: Original transcription data
            
        Returns:
            Dictionary with document information and file path
        """
        try:
            # Create temporary file for PDF
            temp_file = tempfile.NamedTemporaryFile(
                delete=False, 
                suffix='.pdf',
                prefix=f'clinical_notes_{session_id}_'
            )
            temp_file.close()
            
            # Generate PDF document
            doc = SimpleDocTemplate(
                temp_file.name,
                pagesize=letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # Build document content
            story = []
            
            # Header
            story.extend(self._build_header(patient_name, doctor_name, session_id))
            
            # Patient Information Table
            story.extend(self._build_patient_info_table(patient_name, doctor_name, soap_notes))
            
            # SOAP Notes Sections
            story.extend(self._build_soap_sections(soap_notes))
            
            # Quality Assessment Summary
            story.extend(self._build_qa_summary(qa_results))
            
            # Additional Information
            if highlighting_data:
                story.extend(self._build_highlighting_summary(highlighting_data))
            
            if transcription_data:
                story.extend(self._build_transcription_info(transcription_data))
            
            # Footer
            story.extend(self._build_footer())
            
            # Build PDF
            doc.build(story)
            
            # Get file info
            file_size = os.path.getsize(temp_file.name)
            
            result = {
                'document_path': temp_file.name,
                'document_type': 'pdf',
                'file_size_bytes': file_size,
                'patient_name': patient_name,
                'doctor_name': doctor_name,
                'session_id': session_id,
                'generated_at': datetime.now(timezone.utc).isoformat(),
                'specialty': soap_notes.specialty,
                'quality_score': qa_results.quality_score,
                'approved': qa_results.approved
            }
            
            logger.info(f"Generated clinical document for session {session_id}: {temp_file.name}")
            return result
            
        except Exception as e:
            logger.error(f"Document generation failed: {e}")
            # Clean up temp file if it exists
            if 'temp_file' in locals() and os.path.exists(temp_file.name):
                os.unlink(temp_file.name)
            
            raise RuntimeError(f"Failed to generate clinical document: {str(e)}")
    
    def _build_header(self, patient_name: str, doctor_name: str, session_id: str) -> list:
        """Build document header"""
        story = []
        
        # Main title
        story.append(Paragraph("CLINICAL DOCUMENTATION", self.styles['Header']))
        story.append(Spacer(1, 12))
        
        # Document info
        doc_info = f"Session ID: {session_id} | Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        story.append(Paragraph(doc_info, self.styles['Footer']))
        story.append(Spacer(1, 20))
        
        return story
    
    def _build_patient_info_table(self, patient_name: str, doctor_name: str, soap_notes: SOAPNotes) -> list:
        """Build patient information table"""
        story = []
        
        # Patient info table
        data = [
            ['Patient Name:', patient_name],
            ['Attending Physician:', doctor_name],
            ['Medical Specialty:', soap_notes.specialty.title()],
            ['Date of Service:', datetime.now().strftime('%Y-%m-%d')],
            ['ICD-10 Codes:', ', '.join(soap_notes.icd_codes) if soap_notes.icd_codes else 'None assigned']
        ]
        
        table = Table(data, colWidths=[2*inch, 4*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
        
        return story
    
    def _build_soap_sections(self, soap_notes: SOAPNotes) -> list:
        """Build SOAP notes sections"""
        story = []
        
        sections = [
            ('SUBJECTIVE', soap_notes.subjective),
            ('OBJECTIVE', soap_notes.objective),
            ('ASSESSMENT', soap_notes.assessment),
            ('PLAN', soap_notes.plan)
        ]
        
        for section_name, content in sections:
            story.append(Paragraph(section_name, self.styles['SectionHeader']))
            story.append(Paragraph(content or 'No information documented.', self.styles['Clinical']))
            story.append(Spacer(1, 12))
        
        return story
    
    def _build_qa_summary(self, qa_results: QualityAssessment) -> list:
        """Build quality assessment summary"""
        story = []
        
        story.append(Paragraph("QUALITY ASSESSMENT", self.styles['SectionHeader']))
        
        # QA summary table
        qa_data = [
            ['Quality Score:', f"{qa_results.quality_score}/100"],
            ['Approval Status:', 'APPROVED' if qa_results.approved else 'REQUIRES REVIEW'],
            ['Errors Found:', str(len(qa_results.errors))],
            ['Warnings:', str(len(qa_results.warnings))],
            ['Critical Flags:', str(len(qa_results.critical_flags))]
        ]
        
        qa_table = Table(qa_data, colWidths=[2*inch, 2*inch])
        qa_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(qa_table)
        
        # Add errors and warnings if any
        if qa_results.errors:
            story.append(Spacer(1, 12))
            story.append(Paragraph("Errors:", self.styles['Normal']))
            for error in qa_results.errors:
                story.append(Paragraph(f"• {error}", self.styles['Clinical']))
        
        if qa_results.warnings:
            story.append(Spacer(1, 8))
            story.append(Paragraph("Warnings:", self.styles['Normal']))
            for warning in qa_results.warnings:
                story.append(Paragraph(f"• {warning}", self.styles['Clinical']))
        
        if qa_results.recommendations:
            story.append(Spacer(1, 8))
            story.append(Paragraph("Recommendations:", self.styles['Normal']))
            for rec in qa_results.recommendations:
                story.append(Paragraph(f"• {rec}", self.styles['Clinical']))
        
        story.append(Spacer(1, 20))
        
        return story
    
    def _build_highlighting_summary(self, highlighting_data: Dict[str, Any]) -> list:
        """Build highlighting summary section"""
        story = []
        
        if not highlighting_data.get('highlight_summary'):
            return story
        
        story.append(Paragraph("CLINICAL HIGHLIGHTS", self.styles['SectionHeader']))
        
        summary = highlighting_data['highlight_summary']
        
        # Highlight counts
        if 'categories' in summary:
            story.append(Paragraph("Items Highlighted:", self.styles['Normal']))
            for category, count in summary['categories'].items():
                if count > 0:
                    category_name = category.replace('_highlighted', '').replace('_', ' ').title()
                    story.append(Paragraph(f"• {category_name}: {count}", self.styles['Clinical']))
        
        # Priority items
        if summary.get('priority_items'):
            story.append(Spacer(1, 8))
            story.append(Paragraph("Priority Items:", self.styles['Normal']))
            for item in summary['priority_items']:
                story.append(Paragraph(f"• {item}", self.styles['Clinical']))
        
        story.append(Spacer(1, 20))
        
        return story
    
    def _build_transcription_info(self, transcription_data: Dict[str, Any]) -> list:
        """Build transcription information section"""
        story = []
        
        story.append(Paragraph("TRANSCRIPTION DETAILS", self.styles['SectionHeader']))
        
        # Transcription summary
        trans_data = [
            ['Confidence Score:', f"{transcription_data.get('confidence', 0):.2f}"],
            ['Duration:', f"{transcription_data.get('duration', 0):.1f} seconds"],
            ['Language:', transcription_data.get('language', 'Unknown')],
            ['Processing Method:', 'OpenAI Whisper API']
        ]
        
        trans_table = Table(trans_data, colWidths=[2*inch, 2*inch])
        trans_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgreen),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(trans_table)
        story.append(Spacer(1, 20))
        
        return story
    
    def _build_footer(self) -> list:
        """Build document footer"""
        story = []
        
        story.append(Spacer(1, 30))
        footer_text = f"Generated by {settings.app_name} v{settings.app_version} | Confidential Medical Document"
        story.append(Paragraph(footer_text, self.styles['Footer']))
        
        return story
    
    def cleanup_document(self, document_path: str) -> bool:
        """Clean up generated document file"""
        try:
            if os.path.exists(document_path):
                os.unlink(document_path)
                logger.info(f"Cleaned up document: {document_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to cleanup document {document_path}: {e}")
            return False
