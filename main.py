"""
MedScribe Instant - Main Application
FastAPI + Lang<PERSON>hain + Supabase Implementation
"""
import os
import logging
from datetime import datetime, timezone
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles

from config.settings import settings
from database.connection import init_database
from api.routes import recording, rag
from utils.logging_config import setup_logging

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting MedScribe Instant...")
    
    # Initialize database connection
    await init_database()
    
    # Load AI models
    logger.info("Loading AI models...")
    
    yield
    
    # Shutdown
    logger.info("Shutting down MedScribe Instant...")

# Create FastAPI app
app = FastAPI(
    title="MedScribe Instant",
    description="Voice to Clinical Notes AI System with Agentic Flow",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(recording.router, prefix="/api/v1", tags=["soap-generation"])
app.include_router(rag.router, prefix="/api/v1/rag", tags=["rag"])

# Serve static files for frontend
if os.path.exists("frontend/static"):
    app.mount("/static", StaticFiles(directory="frontend/static"), name="static")

# Global exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "path": str(request.url)
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "path": str(request.url)
        }
    )

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "name": "MedScribe Instant",
        "version": "1.0.0",
        "description": "Voice to Clinical Notes AI System with RAG",
        "docs": "/docs",
        "endpoints": {
            "process_audio": "/api/v1/process-audio",
            "process_text": "/api/v1/process-text",
            "rag_embed": "/api/v1/rag/embed",
            "rag_search": "/api/v1/rag/search",
            "rag_summary": "/api/v1/rag/patient/{patient_id}/summary"
        },
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

@app.get("/health")
async def health():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
