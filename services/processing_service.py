"""
Main processing service that orchestrates the enhanced agentic flow
"""
import logging
from datetime import datetime, timezone
from typing import Dict, Any

from fastapi import UploadFile, HTTPException

from config.settings import settings

# Import agents
from agents.medical_validation import MedicalTranscriptionAgent
from agents.soap_generation import SOAPNotesAgent
from agents.quality_assurance import QualityAssuranceAgent

from agents.specialty_formatter import SpecialtyFormatterAgent
from agents.document_generator import ClinicalDocumentGenerator
from agents.specialty_detection import SpecialtyDetectionAgent
from agents.clinical_reasoning import ClinicalReasoningAgent
from agents.safety_check import SafetyCheckAgent
from agents.quality_metrics import QualityMetricsAgent
from agents.final_formatting import FinalFormattingAgent
from services.audio_service import AudioService
from services.database_service import DatabaseService
from services.medical_safety_service import MedicalSafetyValidator
from services.error_handling_service import MedicalErrorHandler, ErrorSeverity
from models.schemas import (
    SessionData, TranscriptionResult, ValidationResult, SOAPNotes,
    SOAPNotesStructured, QualityAssessment, QualityMetrics
)
from utils.serialization import serialize_processing_result

logger = logging.getLogger(__name__)

class ProcessingService:
    """Main service that orchestrates the complete agentic flow"""
    
    def __init__(self):
        # Initialize services
        self.audio_service = AudioService()
        self.database_service = DatabaseService()

        # Initialize agents - New Enhanced Pipeline
        self.validation_agent = MedicalTranscriptionAgent()
        self.specialty_detection_agent = SpecialtyDetectionAgent()
        self.soap_agent = SOAPNotesAgent()
        self.clinical_reasoning_agent = ClinicalReasoningAgent()
        self.specialty_formatter = SpecialtyFormatterAgent()
        self.qa_agent = QualityAssuranceAgent()
        self.safety_check_agent = SafetyCheckAgent()
        self.quality_metrics_agent = QualityMetricsAgent()
        self.final_formatting_agent = FinalFormattingAgent()
        self.document_generator = ClinicalDocumentGenerator()

        # Medical safety and error handling services
        self.medical_safety_validator = MedicalSafetyValidator()
        self.error_handler = MedicalErrorHandler()
    
    async def process_audio_complete_flow(
        self,
        audio_file: UploadFile,
        session_id: str,
        patient_id: str,
        specialty: str,
        doctor_id: str = "default_doctor"
    ) -> Dict[str, Any]:
        """
        Enhanced processing flow with new agentic pipeline:
        Audio Upload → Transcription → Medical Validation → Specialty Detection →
        SOAP Generation → Clinical Reasoning → Specialty Review → Quality Assurance →
        Safety Check → Final Formatting → Database Storage

        Args:
            audio_file: Uploaded audio file
            session_id: Recording session ID
            patient_id: Patient identifier
            specialty: Medical specialty (used as hint for detection)
            doctor_id: Doctor identifier

        Returns:
            Dictionary with processing results
        """
        try:
            # Update session status to processing
            await self.database_service.update_session(session_id, {"status": "processing"})

            # Step 1: Transcribe audio
            logger.info(f"Step 1: Transcribing audio for session {session_id}")
            transcription = await self.audio_service.transcribe_audio(audio_file)

            if transcription.confidence < 0.5:
                logger.warning(f"Low transcription confidence: {transcription.confidence}")

            # Step 2: Validate medical terminology
            logger.info(f"Step 2: Validating medical terminology for session {session_id}")
            validation_result = await self.validation_agent.validate_medical_terminology(
                transcription.text, patient_id, specialty
            )

            # Step 3: Detect specialty dynamically
            logger.info(f"Step 3: Detecting medical specialty for session {session_id}")
            specialty_config = await self.specialty_detection_agent.detect_specialty(
                validation_result.validated_text
            )

            # Step 4: Generate structured SOAP notes
            logger.info(f"Step 4: Generating structured SOAP notes for session {session_id}")
            structured_soap_notes = await self.soap_agent.generate_soap_notes(
                validation_result.validated_text,
                patient_id,
                specialty_config,
                session_id,
                validation_result.flags
            )

            # Step 5: Enhance with clinical reasoning
            logger.info(f"Step 5: Adding clinical reasoning for session {session_id}")
            enhanced_soap_notes = await self.clinical_reasoning_agent.enhance_assessment(
                structured_soap_notes,
                validation_result.validated_text,
                specialty_config
            )

            # Step 6: Calculate quality metrics
            logger.info(f"Step 6: Calculating quality metrics for session {session_id}")
            quality_metrics = await self.quality_metrics_agent.calculate_quality_metrics(
                enhanced_soap_notes,
                validation_result.validated_text,
                specialty_config
            )

            # Step 7: Apply specialty-specific formatting (if needed)
            logger.info(f"Step 7: Applying specialty review for {specialty_config.specialty}")
            # Note: This step can be enhanced or replaced with a dedicated specialty review agent
            # For now, we'll use the existing specialty formatter as a review step
            reviewed_soap_notes = enhanced_soap_notes  # Skip for now, can be enhanced later

            # Step 8: Perform quality assurance
            logger.info(f"Step 8: Performing quality assurance for session {session_id}")
            qa_results = await self.qa_agent.review_soap_notes(
                reviewed_soap_notes, transcription.text, validation_result
            )

            # Check if QA passed
            if not qa_results.approved:
                logger.warning(f"QA failed for session {session_id}: Score {qa_results.quality_score}")
                await self.database_service.update_session(session_id, {
                    "status": "qa_failed",
                    "qa_issues": {
                        "quality_score": qa_results.quality_score,
                        "errors": qa_results.errors,
                        "critical_flags": qa_results.critical_flags
                    }
                })
                # Return detailed QA failure response
                return await self.handle_qa_failed_response(
                    session_id, qa_results, reviewed_soap_notes, transcription, specialty_config
                )

            # Step 9: Create complete SOAP notes with quality metrics
            # Combine the enhanced SOAP notes with quality metrics
            from models.schemas import SOAPNotes
            complete_soap_notes = SOAPNotes(
                soap_notes=reviewed_soap_notes,
                quality_metrics=quality_metrics,
                session_id=session_id,
                specialty=specialty_config.specialty
            )

            # Step 10: Perform safety check
            logger.info(f"Step 10: Performing safety check for session {session_id}")
            safety_checked_notes, safety_result = await self.safety_check_agent.perform_safety_check(
                complete_soap_notes, specialty_config
            )

            # Step 11: Apply final formatting
            logger.info(f"Step 11: Applying final formatting for session {session_id}")
            final_soap_notes = await self.final_formatting_agent.format_final_notes(
                safety_checked_notes, specialty_config
            )

            # Step 12: Generate clinical document
            logger.info(f"Step 12: Generating clinical document for session {session_id}")
            document_result = None
            try:
                document_result = await self.document_generator.generate_clinical_document(
                    final_soap_notes, qa_results, patient_id, doctor_id, session_id,
                    None, transcription.__dict__  # No highlighting result
                )
                logger.info(f"Clinical document generated: {document_result['document_path']}")
            except Exception as e:
                logger.error(f"Document generation failed: {e}")
                # Continue processing even if document generation fails

            # Step 13: Save to database
            logger.info(f"Step 13: Saving enhanced clinical notes for session {session_id}")
            await self.database_service.save_clinical_notes(
                session_id, final_soap_notes, qa_results, transcription, None, patient_id  # Pass patient_id
            )

            # Step 14: Update session as completed
            await self.database_service.update_session(session_id, {
                "status": "completed",
                "processed_at": datetime.now(timezone.utc).isoformat(),
                "document_generated": document_result is not None,
                "specialty_detected": specialty_config.specialty,
                "specialty_confidence": specialty_config.confidence,
                "safety_checked": safety_result.is_safe if safety_result else False
            })

            logger.info(f"Successfully completed enhanced processing for session {session_id}")

            # Create response
            result = {
                "status": "completed",
                "message": "Enhanced processing completed successfully",
                "session_id": session_id,
                "transcription": transcription,
                "validation": validation_result,
                "specialty_detection": {
                    "detected_specialty": specialty_config.specialty,
                    "confidence": specialty_config.confidence,
                    "focus_areas": specialty_config.focus_areas
                },
                "soap_notes": final_soap_notes,
                "quality_metrics": {
                    "completeness_score": final_soap_notes.quality_metrics.completeness_score,
                    "clinical_accuracy": final_soap_notes.quality_metrics.clinical_accuracy,
                    "documentation_quality": final_soap_notes.quality_metrics.documentation_quality,
                    "red_flags": final_soap_notes.quality_metrics.red_flags,
                    "missing_information": final_soap_notes.quality_metrics.missing_information
                },
                "safety_check": {
                    "is_safe": safety_result.is_safe if safety_result else True,
                    "red_flags": safety_result.red_flags if safety_result else [],
                    "critical_items": safety_result.critical_items if safety_result else []
                },
                "qa_results": qa_results,
                "document": document_result,
                "enhanced_pipeline": True
            }

            return serialize_processing_result(result)
            
        except Exception as e:
            logger.error(f"Enhanced processing failed for session {session_id}: {e}", exc_info=True)

            # Update session with error
            await self.database_service.update_session(session_id, {
                "status": "error",
                "error_message": str(e),
                "processed_at": datetime.now(timezone.utc).isoformat()
            })

            # Handle error with medical error handler
            error_response = await self.error_handler.handle_critical_system_error(
                e, {
                    "session_id": session_id,
                    "patient_id": patient_id,
                    "doctor_id": doctor_id,
                    "component": "enhanced_processing_pipeline"
                }
            )

            return {
                "status": "error",
                "message": f"Enhanced processing failed: {str(e)}",
                "session_id": session_id,
                "error_details": error_response,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "enhanced_pipeline": True
            }
    
    async def create_session(
        self,
        doctor_id: str,
        patient_id: str,
        specialty: str
    ) -> SessionData:
        """Create a new recording session"""
        import uuid
        
        session_id = f"session_{int(datetime.now().timestamp())}_{uuid.uuid4().hex[:8]}"
        start_time = datetime.now(timezone.utc)
        
        session_data = SessionData(
            session_id=session_id,
            start_time=start_time,
            status='recording',
            doctor_id=doctor_id,
            patient_id=patient_id,
            specialty=specialty,
            audio_chunks=[]
        )
        
        await self.database_service.create_session(session_data)
        logger.info(f"Created new session: {session_id}")
        
        return session_data
    
    async def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """Get current status of a recording session"""
        session_data = await self.database_service.get_session(session_id)
        if not session_data:
            raise ValueError(f"Session {session_id} not found")
        
        # Get clinical notes if available
        clinical_notes = None
        if session_data.get('status') == 'completed':
            clinical_notes = await self.database_service.get_clinical_notes(session_id)
        
        return {
            "session_id": session_id,
            "status": session_data.get('status'),
            "start_time": session_data.get('start_time'),
            "processed_at": session_data.get('processed_at'),
            "clinical_notes": clinical_notes,
            "ehr_status": session_data.get('ehr_status'),
            "error_message": session_data.get('error_message')
        }
    
    async def get_clinical_notes(self, session_id: str) -> Dict[str, Any]:
        """Get formatted clinical notes for a session"""
        notes_data = await self.database_service.get_clinical_notes(session_id)
        if not notes_data:
            raise ValueError(f"Clinical notes not found for session {session_id}")
        
        return {
            "session_id": session_id,
            "soap_notes": notes_data.get('soap_notes', {}),
            "quality_assessment": notes_data.get('quality_assessment', {}),
            "highlighting": notes_data.get('highlighting_data', {}),
            "transcription_data": notes_data.get('transcription_data', {}),
            "created_at": notes_data.get('created_at')
        }
    
    def _format_soap_text(self, soap_notes: SOAPNotes) -> str:
        """Format SOAP notes as text for highlighting"""
        return f"""SUBJECTIVE: {soap_notes.subjective}

OBJECTIVE: {soap_notes.objective}

ASSESSMENT: {soap_notes.assessment}

PLAN: {soap_notes.plan}"""
    
    async def list_sessions(
        self,
        doctor_id: str = None,
        limit: int = 50
    ) -> Dict[str, Any]:
        """List recording sessions with optional filtering"""
        sessions = await self.database_service.list_sessions(doctor_id, limit)
        return {
            "sessions": sessions,
            "count": len(sessions)
        }
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a recording session and associated data"""
        return await self.database_service.delete_session(session_id)

    async def process_audio_direct(
        self,
        audio_file: UploadFile,
        patient_id: str,
        doctor_id: str,
        specialty: str
    ) -> Dict[str, Any]:
        """
        Enhanced direct audio processing using the new agentic pipeline

        Args:
            audio_file: Uploaded audio file
            patient_id: Patient identifier
            doctor_id: Doctor identifier
            specialty: Medical specialty (used as hint for detection)

        Returns:
            Complete processing results with enhanced SOAP structure
        """
        processing_start = datetime.now(timezone.utc)
        session_id = f"direct_{patient_id}_{int(datetime.now().timestamp())}"

        try:
            logger.info(f"🚀 Starting enhanced direct processing for patient {patient_id}")
            print("\n" + "="*80)
            print("🎉 ENHANCED SOAP GENERATION SYSTEM - DIRECT PROCESSING")
            print("="*80)

            # Use the enhanced pipeline
            result = await self.process_audio_complete_flow(
                audio_file=audio_file,
                session_id=session_id,
                patient_id=patient_id,
                specialty=specialty,
                doctor_id=doctor_id
            )

            processing_time = (datetime.now(timezone.utc) - processing_start).total_seconds()

            # Save enhanced data to database
            enhanced_data = {
                "status": "success",
                "transcription": result.get("transcription"),
                "validation": result.get("validation"),
                "specialty_detection": result.get("specialty_detection"),
                "soap_notes": result.get("soap_notes"),
                "quality_metrics": result.get("quality_metrics"),
                "safety_check": result.get("safety_check"),
                "qa_results": result.get("qa_results"),
                "document": result.get("document"),
                "processing_time": processing_time,
                "processed_at": processing_start.isoformat(),
                "completed_at": datetime.now(timezone.utc).isoformat(),
                "enhanced_pipeline": True
            }

            await self.database_service.save_patient_data(patient_id, enhanced_data)

            print("\n" + "="*80)
            print("✅ ENHANCED PROCESSING COMPLETED SUCCESSFULLY!")
            print(f"⏱️  Total Processing Time: {processing_time:.2f}s")
            print(f"🏥 Detected Specialty: {result.get('specialty_detection', {}).get('detected_specialty', 'Unknown')}")
            print(f"📊 Quality Score: {result.get('quality_metrics', {}).get('completeness_score', 0):.2f}")
            print(f"🛡️  Safety Status: {'SAFE' if result.get('safety_check', {}).get('is_safe', False) else 'NEEDS REVIEW'}")
            print("="*80)

            return {
                "status": "success",
                "message": "Enhanced processing completed successfully",
                "patient_id": patient_id,
                "processing_time_seconds": processing_time,
                "enhanced_pipeline": True,
                **result
            }

        except Exception as e:
            logger.error(f"Enhanced direct processing failed for patient {patient_id}: {e}", exc_info=True)

            processing_time = (datetime.now(timezone.utc) - processing_start).total_seconds()

            # Save error data
            error_data = {
                "status": "error",
                "error_message": str(e),
                "processing_time": processing_time,
                "processed_at": processing_start.isoformat(),
                "enhanced_pipeline": True
            }

            await self.database_service.save_patient_data(patient_id, error_data)

            print("\n" + "="*80)
            print("❌ ENHANCED PROCESSING FAILED")
            print(f"⏱️  Processing Time: {processing_time:.2f}s")
            print(f"🚨 Error: {str(e)}")
            print("="*80)

            return {
                "status": "error",
                "message": f"Enhanced processing failed: {str(e)}",
                "patient_id": patient_id,
                "processing_time_seconds": processing_time,
                "enhanced_pipeline": True,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

            # Step 3: SOAP Notes Generation Agent
            logger.info("Step 3: SOAP Notes Generation Agent")
            print("\n" + "="*60)
            print("📋 STEP 3: SOAP NOTES GENERATION")
            print("="*60)
            soap_notes = await self.soap_agent.generate_soap_notes(
                validation_result.validated_text,
                patient_id,
                specialty,
                f"direct_{int(datetime.now().timestamp())}",
                validation_result.flags
            )
            print(f"📝 Subjective: {soap_notes.subjective[:100]}...")
            print(f"🔍 Objective: {soap_notes.objective[:100]}...")
            print(f"🎯 Assessment: {soap_notes.assessment[:100]}...")
            print(f"📋 Plan: {soap_notes.plan[:100]}...")
            print(f"🏥 Specialty: {getattr(soap_notes, 'specialty', 'Unknown')}")
            print(f"🔢 ICD Codes: {len(soap_notes.icd_codes)} codes")
            print("="*60)

            # Step 3.5: Medical Safety Validation
            print("\n" + "="*60)
            print("🛡️ STEP 3.5: MEDICAL SAFETY VALIDATION")
            print("="*60)
            safety_report = await self.medical_safety_validator.validate_clinical_safety(soap_notes)
            print(f"🛡️ Safety Score: {safety_report.overall_safety_score}/100")
            print(f"🚨 Critical Flags: {len(safety_report.critical_flags)} found")
            print(f"⚠️ High Priority: {len(safety_report.high_priority_flags)} found")
            print(f"💊 Drug Interactions: {len(safety_report.drug_interactions)} found")
            print(f"💉 Dosage Errors: {len(safety_report.dosage_errors)} found")
            print(f"🏥 EHR Safe: {'YES' if safety_report.safe_for_ehr_integration else 'NO'}")
            if safety_report.critical_flags:
                for i, flag in enumerate(safety_report.critical_flags[:2], 1):
                    print(f"   🚨 {i}. {flag.message}")
            print("="*60)

            # Check if safety validation failed critically
            if safety_report.overall_safety_score < 50 or len(safety_report.critical_flags) > 0:
                logger.critical(f"CRITICAL SAFETY FAILURE for patient {patient_id}: Score {safety_report.overall_safety_score}")
                print("\n" + "="*60)
                print("🚨 CRITICAL SAFETY FAILURE - PROCESSING STOPPED")
                print("="*60)
                print(f"⛔ Safety score too low: {safety_report.overall_safety_score}/100")
                print(f"🚨 Critical safety issues detected")
                print(f"🏥 IMMEDIATE PHYSICIAN REVIEW REQUIRED")
                print("="*60)

                # Save critical safety failure to database
                await self.database_service.save_patient_data(
                    patient_id, {
                        "status": "critical_safety_failure",
                        "transcription": transcription.__dict__,
                        "validation": validation_result.__dict__,
                        "soap_notes": soap_notes.__dict__,
                        "safety_report": safety_report.__dict__,
                        "processed_at": processing_start.isoformat()
                    }
                )

                return {
                    "status": "critical_safety_failure",
                    "message": "CRITICAL SAFETY ISSUES DETECTED - Immediate physician review required",
                    "patient_id": patient_id,
                    "safety_report": {
                        "safety_score": safety_report.overall_safety_score,
                        "critical_flags": [f.message for f in safety_report.critical_flags],
                        "drug_interactions": safety_report.drug_interactions,
                        "dosage_errors": safety_report.dosage_errors,
                        "requires_immediate_attention": True
                    },
                    "next_steps": "STOP ALL PROCESSING - Contact attending physician immediately",
                    "review_required_by": "attending_physician_immediately"
                }

            # Step 4: Quality Assurance Agent
            logger.info("Step 4: Quality Assurance Agent")
            print("\n" + "="*60)
            print("🔍 STEP 4: QUALITY ASSURANCE")
            print("="*60)
            qa_results = await self.qa_agent.review_soap_notes(
                soap_notes, transcription.text, validation_result
            )
            print(f"📊 Quality Score: {qa_results.quality_score}/100")
            print(f"✅ Approved: {'YES' if qa_results.approved else 'NO'}")
            print(f"❌ Errors: {len(qa_results.errors)} found")
            print(f"⚠️  Warnings: {len(qa_results.warnings)} found")
            print(f"🚩 Critical Flags: {len(qa_results.critical_flags)} found")
            if qa_results.errors:
                for i, error in enumerate(qa_results.errors[:2], 1):  # Show first 2 errors
                    print(f"   {i}. {error}")
            print("="*60)

            # Step 5: QA Approval Check
            if not qa_results.approved:
                logger.warning(f"QA failed for patient {patient_id}: Score {qa_results.quality_score}")
                print("\n" + "="*60)
                print("❌ QA FAILED - MANUAL REVIEW REQUIRED")
                print("="*60)
                print(f"🚫 Processing stopped due to quality issues")
                print(f"📋 Manual review required by attending physician")
                print("="*60)

                # Save failed QA data to database
                await self.database_service.save_patient_data(
                    patient_id, {
                        "status": "quality_assurance_failed",
                        "transcription": transcription.__dict__,
                        "validation": validation_result.__dict__,
                        "soap_notes": soap_notes.__dict__,
                        "qa_results": qa_results.__dict__,
                        "processed_at": processing_start.isoformat()
                    }
                )

                return self._create_qa_failed_response(
                    patient_id, qa_results, soap_notes, transcription
                )

            # Step 6: Specialty Format Check
            logger.info("Step 6: Specialty Format Check")
            print("\n" + "="*60)
            print("🏥 STEP 6: SPECIALTY FORMAT CHECK")
            print("="*60)
            specialty_check = self._check_specialty_formatting_required(specialty)
            print(f"🎯 Specialty: {specialty}")
            print(f"✅ Requires Special Formatting: {'YES' if specialty_check['requires_special_formatting'] else 'NO'}")
            print("="*60)

            # Step 7: Specialty-Specific Formatter (if needed)
            formatted_soap_notes = soap_notes
            if specialty_check["requires_special_formatting"]:
                logger.info("Step 7: Specialty-Specific Formatter")
                print("\n" + "="*60)
                print("🎨 STEP 7: SPECIALTY-SPECIFIC FORMATTER")
                print("="*60)
                formatted_soap_notes = await self.specialty_formatter.apply_specialty_formatting(
                    soap_notes, specialty, transcription.text
                )
                print(f"✅ Applied {specialty} specific formatting")
                print("="*60)

            # Step 8: Smart Highlighting Engine
            logger.info("Step 8: Smart Highlighting Engine")
            print("\n" + "="*60)
            print("🌟 STEP 8: SMART HIGHLIGHTING ENGINE")
            print("="*60)
            soap_text = self._format_soap_text(formatted_soap_notes)
            highlighting_result = self.highlighting_service.apply_smart_highlighting(
                soap_text, qa_results
            )
            print(f"✅ Applied smart highlighting")
            print(f"📝 HTML length: {len(highlighting_result.get('highlighted_notes', ''))} chars")
            print("="*60)

            # Step 9: Create Clinical Document
            logger.info("Step 9: Create Clinical Document")
            print("\n" + "="*60)
            print("📄 STEP 9: CREATE CLINICAL DOCUMENT")
            print("="*60)
            document_result = None
            try:
                document_result = await self.document_generator.generate_clinical_document(
                    formatted_soap_notes, qa_results, patient_id, doctor_id,
                    f"direct_{patient_id}_{int(datetime.now().timestamp())}"
                )
                print(f"✅ Document generated successfully")
                print(f"📁 Path: {document_result.get('document_path', 'N/A')}")
            except Exception as e:
                logger.error(f"Document generation failed: {e}")
                print(f"❌ Document generation failed: {e}")
            print("="*60)

            # Step 10: EHR Integration Agent
            logger.info("Step 10: EHR Integration Agent")
            print("\n" + "="*60)
            print("🏥 STEP 10: EHR INTEGRATION")
            print("="*60)
            ehr_result = None
            try:
                ehr_result = await self.ehr_integration.integrate_clinical_notes(
                    formatted_soap_notes, qa_results, patient_id, doctor_id
                )
                print(f"✅ EHR integration: {'SUCCESS' if ehr_result and ehr_result.success else 'NOT CONFIGURED'}")
                if ehr_result and ehr_result.success:
                    print(f"🆔 Encounter ID: {ehr_result.encounter_id}")
            except Exception as e:
                logger.error(f"EHR integration failed: {e}")
                print(f"❌ EHR integration failed: {e}")
            print("="*60)

            # Step 11: Save to Supabase for patient
            processing_time = (datetime.now(timezone.utc) - processing_start).total_seconds()
            print("\n" + "="*60)
            print("💾 STEP 11: SAVE TO DATABASE")
            print("="*60)

            patient_data = {
                "status": "success",
                "transcription": transcription.__dict__,
                "validation": validation_result.__dict__,
                "soap_notes": formatted_soap_notes.__dict__,
                "original_soap_notes": soap_notes.__dict__,
                "qa_results": qa_results.__dict__,
                "highlighting": highlighting_result,
                "document": document_result,
                "ehr_integration": ehr_result.__dict__ if ehr_result else None,
                "specialty_check": specialty_check,
                "processing_time": processing_time,
                "processed_at": processing_start.isoformat(),
                "completed_at": datetime.now(timezone.utc).isoformat()
            }

            await self.database_service.save_patient_data(patient_id, patient_data)
            print(f"✅ Patient data saved to Supabase")
            print(f"🆔 Patient ID: {patient_id}")
            print(f"⏱️  Total Processing Time: {processing_time:.2f}s")
            print("="*60)

            # Step 12: Create Final Response
            print("\n" + "="*60)
            print("🎉 STEP 12: PROCESSING COMPLETE!")
            print("="*60)
            print(f"✅ All 12 workflow steps completed successfully")
            print(f"📋 Clinical notes generated and validated")
            print(f"💾 Data saved to database")
            print(f"🎯 Ready for clinical use")
            print("="*60)

            return self._create_final_response(
                patient_id, doctor_id, transcription, validation_result,
                formatted_soap_notes, qa_results, highlighting_result,
                document_result, ehr_result, specialty_check, processing_time
            )

        except Exception as e:
            logger.error(f"Direct processing failed for patient {patient_id}: {e}", exc_info=True)

            # Save error to database
            await self.database_service.save_patient_data(
                patient_id, {
                    "status": "error",
                    "error_message": str(e),
                    "processed_at": processing_start.isoformat()
                }
            )

            raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

    async def handle_qa_failed_response(
        self,
        session_id: str,
        qa_results: QualityAssessment,
        soap_notes: SOAPNotes,
        transcription: TranscriptionResult,
        specialty_config
    ) -> Dict[str, Any]:
        """
        Handle QA failed response with detailed error analysis and recommendations

        Args:
            session_id: Session identifier
            qa_results: Failed QA results
            soap_notes: Original SOAP notes
            transcription: Transcription data

        Returns:
            Detailed error response with guidance
        """
        logger.warning(f"Handling QA failure for session {session_id}")

        # Categorize errors and warnings
        critical_errors = [error for error in qa_results.errors if any(
            keyword in error.lower() for keyword in ['critical', 'dangerous', 'contraindication', 'allergy']
        )]

        documentation_errors = [error for error in qa_results.errors if any(
            keyword in error.lower() for keyword in ['incomplete', 'missing', 'unclear', 'documentation']
        )]

        medical_errors = [error for error in qa_results.errors
                         if error not in critical_errors and error not in documentation_errors]

        # Generate specific recommendations
        recommendations = []

        if critical_errors:
            recommendations.extend([
                "URGENT: Review critical safety concerns immediately",
                "Verify patient allergies and contraindications",
                "Double-check medication dosages and interactions"
            ])

        if documentation_errors:
            recommendations.extend([
                "Complete missing documentation sections",
                "Clarify unclear medical terminology",
                "Add required specialty-specific details"
            ])

        if medical_errors:
            recommendations.extend([
                "Review medical accuracy and terminology",
                "Verify clinical reasoning and assessments",
                "Ensure proper ICD-10 code assignments"
            ])

        # Determine next steps based on error severity
        if qa_results.quality_score < 50:
            next_steps = [
                "Manual review by attending physician required",
                "Consider re-recording if transcription quality is poor",
                "Do not proceed with EHR integration"
            ]
        elif qa_results.quality_score < 70:
            next_steps = [
                "Address identified errors and reprocess",
                "Senior physician review recommended",
                "Hold EHR integration pending corrections"
            ]
        else:
            next_steps = [
                "Minor corrections needed",
                "Resident physician review acceptable",
                "May proceed with manual EHR entry after review"
            ]

        # Create detailed response
        response = {
            "status": "qa_failed",
            "message": "Quality assurance failed - Manual review required",
            "session_id": session_id,
            "qa_summary": {
                "quality_score": qa_results.quality_score,
                "approval_threshold": settings.min_quality_score,
                "total_errors": len(qa_results.errors),
                "total_warnings": len(qa_results.warnings),
                "critical_flags": len(qa_results.critical_flags)
            },
            "error_analysis": {
                "critical_errors": critical_errors,
                "documentation_errors": documentation_errors,
                "medical_errors": medical_errors,
                "warnings": qa_results.warnings,
                "critical_flags": qa_results.critical_flags
            },
            "recommendations": recommendations,
            "next_steps": next_steps,
            "reprocessing_options": {
                "can_reprocess": True,
                "requires_manual_review": qa_results.quality_score < 60,
                "ehr_integration_blocked": True
            },
            "original_data": {
                "transcription_confidence": transcription.confidence,
                "specialty": specialty_config.specialty,
                "sections_completed": self._count_completed_sections(soap_notes)
            }
        }

        return response

    def _count_completed_sections(self, soap_notes: SOAPNotes) -> Dict[str, bool]:
        """Count which SOAP sections are adequately completed"""
        min_length = 20  # Minimum characters for a section to be considered complete

        return {
            "subjective": len(soap_notes.subjective.strip()) >= min_length,
            "objective": len(soap_notes.objective.strip()) >= min_length,
            "assessment": len(soap_notes.assessment.strip()) >= min_length,
            "plan": len(soap_notes.plan.strip()) >= min_length
        }

    def _check_specialty_formatting_required(self, specialty: str) -> Dict[str, Any]:
        """Check if specialty formatting is required"""
        return {
            "specialty_match": specialty.lower() in ["cardiology", "dermatology", "orthopedics", "neurology", "pediatrics"],
            "requires_special_formatting": specialty.lower() != "internal_medicine" and specialty.lower() != "general",
            "route_to": "specialty_formatter" if specialty.lower() != "internal_medicine" else "smart_highlighting"
        }

    def _create_qa_failed_response(
        self,
        patient_id: str,
        qa_results: QualityAssessment,
        soap_notes: SOAPNotes,
        transcription: TranscriptionResult
    ) -> Dict[str, Any]:
        """Create QA failed response matching your output structure"""
        return {
            "status": "quality_assurance_failed",
            "message": "Clinical notes require manual review before processing",
            "patient_id": patient_id,
            "qa_results": {
                "quality_score": qa_results.quality_score,
                "approved": qa_results.approved,
                "critical_errors": qa_results.errors,
                "required_actions": [
                    "Manual physician review required",
                    "Clarification needed on treatment recommendations",
                    "Additional patient history documentation needed"
                ]
            },
            "next_steps": "Manual review and correction required before EHR integration",
            "review_required_by": "attending_physician"
        }

    def _create_final_response(
        self,
        patient_id: str,
        doctor_id: str,
        transcription: TranscriptionResult,
        validation_result: ValidationResult,
        soap_notes: SOAPNotes,
        qa_results: QualityAssessment,
        highlighting_result: Dict[str, Any],
        document_result: Dict[str, Any],
        ehr_result: Any,
        specialty_check: Dict[str, Any],
        processing_time: float
    ) -> Dict[str, Any]:
        """Create final response matching your output structure"""

        # Format processing time as MM:SS
        minutes = int(processing_time // 60)
        seconds = int(processing_time % 60)
        formatted_time = f"{minutes:02d}:{seconds:02d}"

        return {
            "status": "success",
            "message": "Clinical notes successfully processed and integrated",
            "patient_id": patient_id,
            "processing_summary": {
                "transcription": "completed",
                "validation": "completed",
                "soap_generation": "completed",
                "quality_assurance": f"passed (score: {qa_results.quality_score})",
                "highlighting": "completed",
                "document_creation": "completed" if document_result else "failed",
                "ehr_integration": "completed" if ehr_result and ehr_result.success else "not_configured"
            },
            "deliverables": {
                "clinical_notes": {
                    "subjective": soap_notes.subjective,
                    "objective": soap_notes.objective,
                    "assessment": soap_notes.assessment,
                    "plan": soap_notes.plan
                },
                "highlighted_html": highlighting_result.get('highlighted_notes', ''),
                "google_doc_url": document_result.get('document_path', '') if document_result else None,
                "ehr_record_id": ehr_result.encounter_id if ehr_result and ehr_result.success else None
            },
            "recommendations": qa_results.recommendations,
            "total_processing_time": formatted_time,
            "completed_at": datetime.now(timezone.utc).isoformat()
        }
