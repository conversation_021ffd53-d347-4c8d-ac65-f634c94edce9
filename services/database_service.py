"""
Database service for Supabase operations
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

from fastapi import HTTPException

from config.settings import settings
from models.schemas import SessionData, SOAPNotes, QualityAssessment, TranscriptionResult

# Import Supabase client
from supabase import create_client, Client

logger = logging.getLogger(__name__)

class DatabaseService:
    """Service for database operations using Supabase"""

    def __init__(self):
        self.supabase: Client = create_client(
            settings.supabase_url,
            settings.supabase_anon_key
        )
        logger.info("Database service initialized with Supabase")
    
    async def create_session(self, session_data: SessionData) -> Dict[str, Any]:
        """Create a new recording session"""
        try:
            result = self.supabase.table('recording_sessions').insert(
                session_data.to_dict()
            ).execute()

            if result.data:
                logger.info(f"Created session: {session_data.session_id}")
                return result.data[0]
            else:
                raise Exception("No data returned from insert")

        except Exception as e:
            logger.error(f"Failed to create session: {e}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    
    async def update_session(self, session_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update a recording session"""
        try:
            # Add updated timestamp
            updates['updated_at'] = datetime.now(timezone.utc).isoformat()

            result = self.supabase.table('recording_sessions').update(updates).eq(
                'session_id', session_id
            ).execute()

            if result.data:
                logger.debug(f"Updated session {session_id}: {list(updates.keys())}")
                return result.data[0]
            else:
                logger.warning(f"No session found to update: {session_id}")
                return {}

        except Exception as e:
            logger.error(f"Failed to update session {session_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get a recording session by ID"""
        try:
            result = self.supabase.table('recording_sessions').select('*').eq(
                'session_id', session_id
            ).execute()

            if result.data:
                return result.data[0]
            return None

        except Exception as e:
            logger.error(f"Failed to get session {session_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    
    async def save_clinical_notes(
        self,
        session_id: str,
        soap_notes: SOAPNotes,
        qa_results: QualityAssessment,
        transcription: TranscriptionResult,
        highlighting_data: Dict[str, Any] = None,
        patient_id: str = None
    ) -> Dict[str, Any]:
        """Save enhanced clinical notes and related data"""
        try:
            from dataclasses import asdict

            # Convert the enhanced SOAP notes structure to a serializable format
            soap_notes_dict = self._serialize_enhanced_soap_notes(soap_notes)

            # Extract patient_id from session_id if not provided
            if not patient_id:
                # Extract patient_id from session_id format: "audio_patient_id_timestamp" or "text_patient_id_timestamp"
                parts = session_id.split('_')
                if len(parts) >= 3:
                    patient_id = '_'.join(parts[1:-1])  # Everything between first and last underscore
                else:
                    patient_id = "unknown_patient"

            clinical_data = {
                'session_id': session_id,
                'patient_id': patient_id,
                'soap_notes': soap_notes_dict,
                'quality_assessment': asdict(qa_results),
                'transcription_data': asdict(transcription),
                'highlighting_data': highlighting_data or {},
                'icd_codes': soap_notes.icd_codes,  # Legacy compatibility
                'specialty': soap_notes.specialty,
                'quality_metrics': asdict(soap_notes.quality_metrics),
                'completeness_score': soap_notes.quality_metrics.completeness_score,
                'clinical_accuracy': soap_notes.quality_metrics.clinical_accuracy,
                'documentation_quality': soap_notes.quality_metrics.documentation_quality,
                'red_flags': soap_notes.quality_metrics.red_flags,
                'missing_information': soap_notes.quality_metrics.missing_information,
                'created_at': datetime.now(timezone.utc).isoformat()
            }

            result = self.supabase.table('clinical_notes').insert(clinical_data).execute()

            if result.data:
                logger.info(f"Saved enhanced clinical notes for session: {session_id}")
                return result.data[0]
            else:
                raise Exception("No data returned from insert")

        except Exception as e:
            logger.error(f"Failed to save clinical notes for {session_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

    def _serialize_enhanced_soap_notes(self, soap_notes: SOAPNotes) -> Dict[str, Any]:
        """Convert enhanced SOAP notes to a serializable dictionary"""
        from dataclasses import asdict

        try:
            # Convert the entire structure to dict
            soap_dict = asdict(soap_notes.soap_notes)

            # Ensure all nested objects are properly serialized
            return {
                "subjective": soap_dict["subjective"],
                "objective": soap_dict["objective"],
                "assessment": soap_dict["assessment"],
                "plan": soap_dict["plan"],
                "clinical_notes": soap_dict["clinical_notes"]
            }
        except Exception as e:
            logger.error(f"Error serializing SOAP notes: {e}")
            # Fallback to legacy format
            return {
                "subjective": soap_notes.subjective,
                "objective": soap_notes.objective,
                "assessment": soap_notes.assessment,
                "plan": soap_notes.plan,
                "clinical_notes": getattr(soap_notes.soap_notes, 'clinical_notes', '')
            }
    
    async def get_clinical_notes(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get clinical notes for a session"""
        try:
            result = self.supabase.table('clinical_notes').select('*').eq(
                'session_id', session_id
            ).execute()

            if result.data:
                return result.data[0]
            return None

        except Exception as e:
            logger.error(f"Failed to get clinical notes for {session_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    
    async def list_sessions(
        self,
        doctor_id: str = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """List recording sessions with optional filtering"""
        try:
            query = self.supabase.table('recording_sessions').select('*')

            if doctor_id:
                query = query.eq('doctor_id', doctor_id)

            result = query.order('start_time', desc=True).limit(limit).execute()

            return result.data or []

        except Exception as e:
            logger.error(f"Failed to list sessions: {e}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a recording session and associated data"""
        try:
            # Delete clinical notes first (foreign key constraint)
            self.supabase.table('clinical_notes').delete().eq(
                'session_id', session_id
            ).execute()

            # Delete voice commands
            self.supabase.table('voice_commands').delete().eq(
                'session_id', session_id
            ).execute()

            # Delete session
            result = self.supabase.table('recording_sessions').delete().eq(
                'session_id', session_id
            ).execute()

            if result.data:
                logger.info(f"Deleted session: {session_id}")
                return True
            else:
                logger.warning(f"No session found to delete: {session_id}")
                return False

        except Exception as e:
            logger.error(f"Failed to delete session {session_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    
    async def save_patient_data(self, patient_id: str, data: Dict[str, Any]) -> bool:
        """Save enhanced patient processing data directly to Supabase"""
        try:
            # Prepare enhanced data for insertion
            patient_record = {
                'patient_id': patient_id,
                'status': data.get('status'),
                'transcription_data': data.get('transcription'),
                'validation_data': data.get('validation'),
                'soap_notes': data.get('soap_notes'),
                'qa_results': data.get('qa_results'),
                'document_data': data.get('document'),
                'processing_time': data.get('processing_time'),
                'processed_at': data.get('processed_at'),
                'completed_at': data.get('completed_at'),
                'error_message': data.get('error_message'),
                # Enhanced fields
                'specialty_detection': data.get('specialty_detection'),
                'quality_metrics': data.get('quality_metrics'),
                'safety_check': data.get('safety_check'),
                'enhanced_pipeline': data.get('enhanced_pipeline', True),
                'updated_at': datetime.now(timezone.utc).isoformat()
            }

            # Insert or update patient record
            self.supabase.table('patient_records').upsert(
                patient_record,
                on_conflict='patient_id'
            ).execute()

            logger.info(f"Saved patient data for: {patient_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to save patient data for {patient_id}: {e}")
            return False

    async def get_patient_data(self, patient_id: str) -> Dict[str, Any]:
        """Get patient processing data from Supabase"""
        if not self.supabase:
            logger.info(f"Demo mode: Simulated getting patient data for {patient_id}")
            return {
                'patient_id': patient_id,
                'status': 'success',
                'processed_at': datetime.now(timezone.utc).isoformat()
            }

        try:
            result = self.supabase.table('patient_records').select('*').eq('patient_id', patient_id).execute()

            if result.data:
                return result.data[0]
            return None

        except Exception as e:
            logger.error(f"Failed to get patient data for {patient_id}: {e}")
            return None
    
    async def health_check(self) -> Dict[str, str]:
        """Check database connectivity"""
        try:
            # Simple query to test connection
            self.supabase.table('recording_sessions').select('count').limit(1).execute()
            return {"status": "connected", "message": "Database is accessible"}

        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {"status": "error", "message": str(e)}
