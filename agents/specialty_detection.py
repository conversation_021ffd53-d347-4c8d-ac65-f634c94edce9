"""
Specialty Detection Agent
Automatically detects medical specialty from transcriptions and generates dynamic configuration
"""
import json
import logging
from typing import Dict, List, Any
from dataclasses import dataclass

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

from config.settings import settings

logger = logging.getLogger(__name__)

@dataclass
class SpecialtyConfiguration:
    """Dynamic specialty configuration"""
    specialty: str
    confidence: float
    focus_areas: List[str]
    required_sections: List[str]
    icd_codes_prefix: List[str]
    common_medications: List[str]
    vital_signs_focus: List[str]
    red_flags: List[str]
    quality_indicators: List[str]

class SpecialtyDetectionAgent:
    """Agent for automatically detecting medical specialty and generating dynamic configuration"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.openai_model,
            temperature=0.1,  # Very low temperature for consistent specialty detection
            max_tokens=1500,
            openai_api_key=settings.openai_api_key
        )
    
    async def detect_specialty(self, transcription: str) -> SpecialtyConfiguration:
        """
        Analyze transcription to detect medical specialty and generate configuration
        
        Args:
            transcription: Medical transcription text
            
        Returns:
            SpecialtyConfiguration with detected specialty and dynamic config
        """
        system_prompt = self._get_detection_system_prompt()
        user_prompt = self._get_detection_user_prompt(transcription)
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        try:
            response = await self.llm.agenerate([messages])
            result_text = response.generations[0][0].text
            
            specialty_config = self._parse_specialty_response(result_text)
            
            logger.info(f"Detected specialty: {specialty_config.specialty} "
                       f"(confidence: {specialty_config.confidence:.2f})")
            return specialty_config
            
        except Exception as e:
            logger.error(f"Specialty detection failed: {e}")
            # Return default general configuration
            return self._get_fallback_configuration()
    
    def _get_detection_system_prompt(self) -> str:
        """Get system prompt for specialty detection"""
        return """You are a Medical Specialty Detection Expert. Your task is to analyze medical transcriptions and determine the primary medical specialty involved.

ANALYSIS REQUIREMENTS:
1. Identify the primary medical specialty based on:
   - Medical terminology used
   - Types of examinations mentioned
   - Diagnostic procedures discussed
   - Treatment approaches
   - Anatomical systems involved
   - Medication classes mentioned

2. Generate specialty-specific configuration including:
   - Focus areas relevant to this specialty
   - Required documentation sections
   - Relevant ICD-10 code prefixes
   - Common medications for this specialty
   - Vital signs priorities
   - Red flags specific to this specialty
   - Quality indicators for clinical assessment

SUPPORTED SPECIALTIES:
- Cardiology
- Dermatology
- Orthopedics
- Neurology
- Pediatrics
- Psychiatry
- Gastroenterology
- Pulmonology
- Endocrinology
- Oncology
- Emergency Medicine
- Family Medicine
- Internal Medicine
- General (for unclear cases)

OUTPUT FORMAT:
Return ONLY valid JSON in this exact format:
{
  "specialty": "detected_specialty",
  "confidence": 0.0-1.0,
  "focus_areas": ["area1", "area2", "area3"],
  "required_sections": ["section1", "section2"],
  "icd_codes_prefix": ["prefix1", "prefix2"],
  "common_medications": ["med1", "med2", "med3"],
  "vital_signs_focus": ["vital1", "vital2"],
  "red_flags": ["flag1", "flag2", "flag3"],
  "quality_indicators": ["indicator1", "indicator2"]
}

Be precise and clinically accurate. If uncertain, use "general" specialty with lower confidence."""
    
    def _get_detection_user_prompt(self, transcription: str) -> str:
        """Get user prompt for specialty detection"""
        return f"""Analyze this medical transcription and determine the primary medical specialty:

TRANSCRIPTION:
{transcription}

Please analyze the content and return the specialty detection results in the specified JSON format."""
    
    def _parse_specialty_response(self, response_text: str) -> SpecialtyConfiguration:
        """Parse LLM response into SpecialtyConfiguration object"""
        try:
            result = json.loads(response_text)
            
            return SpecialtyConfiguration(
                specialty=result.get('specialty', 'general'),
                confidence=float(result.get('confidence', 0.5)),
                focus_areas=result.get('focus_areas', []),
                required_sections=result.get('required_sections', []),
                icd_codes_prefix=result.get('icd_codes_prefix', []),
                common_medications=result.get('common_medications', []),
                vital_signs_focus=result.get('vital_signs_focus', []),
                red_flags=result.get('red_flags', []),
                quality_indicators=result.get('quality_indicators', [])
            )
            
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            logger.error(f"Failed to parse specialty detection response: {e}")
            logger.debug(f"Response text: {response_text}")
            return self._get_fallback_configuration()
    
    def _get_fallback_configuration(self) -> SpecialtyConfiguration:
        """Get fallback configuration for when detection fails"""
        return SpecialtyConfiguration(
            specialty="general",
            confidence=0.3,
            focus_areas=["chief complaint", "review of systems", "physical examination"],
            required_sections=["history of present illness", "physical exam", "assessment and plan"],
            icd_codes_prefix=[],
            common_medications=[],
            vital_signs_focus=["blood pressure", "heart rate", "temperature", "respiratory rate"],
            red_flags=["chest pain", "shortness of breath", "severe pain", "altered mental status"],
            quality_indicators=["complete history", "thorough examination", "clear assessment", "appropriate plan"]
        )
    
    def get_specialty_config_dict(self, config: SpecialtyConfiguration) -> Dict[str, Any]:
        """Convert SpecialtyConfiguration to dictionary format for backward compatibility"""
        return {
            'specialty': config.specialty,
            'confidence': config.confidence,
            'focus_areas': config.focus_areas,
            'required_sections': config.required_sections,
            'icd_codes_prefix': config.icd_codes_prefix,
            'common_medications': config.common_medications,
            'vital_signs_focus': config.vital_signs_focus,
            'red_flags': config.red_flags,
            'quality_indicators': config.quality_indicators
        }
