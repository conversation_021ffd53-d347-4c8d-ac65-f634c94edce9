"""
Quality Assurance Agent
"""
import json
import logging
from typing import List, Dict, Any

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

from config.settings import settings
from models.schemas import QualityAssessment, SOAPNotes, ValidationResult

logger = logging.getLogger(__name__)

class QualityAssuranceAgent:
    """Agent for reviewing and validating clinical documentation quality"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.openai_model,
            temperature=0.1,  
            max_tokens=1500,
            openai_api_key=settings.openai_api_key
        )
    
    async def review_soap_notes(
        self,
        soap_notes: SOAPNotes,
        original_transcription: str,
        validation_results: ValidationResult
    ) -> QualityAssessment:
        """
        Review SOAP notes for completeness, accuracy, and safety
        
        Args:
            soap_notes: Generated SOAP notes
            original_transcription: Original transcription text
            validation_results: Results from medical validation
            
        Returns:
            QualityAssessment with scores and recommendations
        """
        system_prompt = self._get_qa_system_prompt()
        user_prompt = self._get_qa_user_prompt(
            soap_notes, original_transcription, validation_results
        )
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        try:
            response = await self.llm.agenerate([messages])
            result_text = response.generations[0][0].text
            
            qa_assessment = self._parse_qa_response(result_text)
            
            # Add additional automated checks
            qa_assessment = self._add_automated_checks(qa_assessment, soap_notes)
            
            logger.info(f"QA completed - Score: {qa_assessment.quality_score}, Approved: {qa_assessment.approved}")
            return qa_assessment
            
        except Exception as e:
            logger.error(f"Quality assurance failed: {e}")
            return QualityAssessment(
                quality_score=50,
                errors=[f"QA process failed: {str(e)}"],
                warnings=["Quality assessment could not be completed"],
                recommendations=["Manual review required"],
                critical_flags=[],
                approved=False
            )
    
    def _get_qa_system_prompt(self) -> str:
        """Get system prompt for quality assurance"""
        return """You are a Quality Assurance Agent for clinical documentation.

Your role is to:
1. Review SOAP notes for completeness and accuracy
2. Flag potential errors, inconsistencies, or missing information
3. Verify medication dosages and contraindications
4. Check for logical consistency in clinical reasoning
5. Ensure compliance with medical documentation standards
6. Identify any critical safety concerns

Evaluation criteria:
- Completeness: All SOAP sections adequately documented
- Accuracy: Medical terminology and facts are correct
- Consistency: Information aligns across sections
- Safety: No dangerous medication interactions or dosages
- Clarity: Documentation is clear and professional
- Compliance: Meets medical documentation standards

Return JSON with:
{
    "qualityScore": 0-100,
    "errors": ["list of errors found"],
    "warnings": ["list of warnings"],
    "recommendations": ["list of improvement recommendations"],
    "criticalFlags": [{"type": "safety", "message": "description", "severity": "high/medium/low"}],
    "approved": boolean
}

Quality Score Guidelines:
- 90-100: Excellent, minimal issues
- 80-89: Good, minor improvements needed
- 70-79: Acceptable, some issues to address
- 60-69: Below standard, significant issues
- Below 60: Unacceptable, major revision needed

Approval threshold: Score >= 70 AND no critical safety flags

Return only valid JSON without additional text."""
    
    def _get_qa_user_prompt(
        self,
        soap_notes: SOAPNotes,
        original_transcription: str,
        validation_results: ValidationResult
    ) -> str:
        """Get user prompt for QA review"""
        # Handle both old and new SOAP note formats
        if hasattr(soap_notes, 'soap_notes'):
            # New enhanced format - use legacy properties
            subjective = str(soap_notes.subjective) if soap_notes.subjective else 'Not available'
            objective = str(soap_notes.objective) if soap_notes.objective else 'Not available'
            assessment = str(soap_notes.assessment) if soap_notes.assessment else 'Not available'
            plan = str(soap_notes.plan) if soap_notes.plan else 'Not available'
        else:
            # Old format (fallback)
            subjective = str(getattr(soap_notes, 'subjective', 'Not available'))
            objective = str(getattr(soap_notes, 'objective', 'Not available'))
            assessment = str(getattr(soap_notes, 'assessment', 'Not available'))
            plan = str(getattr(soap_notes, 'plan', 'Not available'))

        soap_text = f"""
SUBJECTIVE: {subjective}
OBJECTIVE: {objective}
ASSESSMENT: {assessment}
PLAN: {plan}
"""
        
        return f"""Review these SOAP notes for quality assurance:

SOAP Notes:
{soap_text}

Original Transcription: {original_transcription}

Validation Results:
- Confidence: {validation_results.confidence}
- Corrections: {len(validation_results.corrections)} made
- Flags: {len(validation_results.flags)} raised

Specialty: {soap_notes.specialty if hasattr(soap_notes, 'specialty') else 'Unknown'}

Please provide a comprehensive quality assessment."""
    
    def _parse_qa_response(self, response_text: str) -> QualityAssessment:
        """Parse LLM response into QualityAssessment object"""
        try:
            result = json.loads(response_text)
            return QualityAssessment(
                quality_score=result.get('qualityScore', 70),
                errors=result.get('errors', []),
                warnings=result.get('warnings', []),
                recommendations=result.get('recommendations', []),
                critical_flags=result.get('criticalFlags', []),
                approved=result.get('approved', True)
            )
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse QA response: {e}")
            logger.debug(f"Response text: {response_text}")
            return QualityAssessment(
                quality_score=50,
                errors=["Failed to parse QA response"],
                warnings=["Quality assessment parsing failed"],
                recommendations=["Manual review required"],
                critical_flags=[],
                approved=False
            )
    
    def _add_automated_checks(
        self,
        qa_assessment: QualityAssessment,
        soap_notes: SOAPNotes
    ) -> QualityAssessment:
        """Add automated quality checks"""
        
        # Check section completeness
        min_length = 10  # Minimum characters per section
        sections = {
            'Subjective': soap_notes.subjective,
            'Objective': soap_notes.objective,
            'Assessment': soap_notes.assessment,
            'Plan': soap_notes.plan
        }
        
        for section_name, content in sections.items():
            # Convert content to string if it's not already
            content_str = str(content) if content else ""
            if len(content_str.strip()) < min_length:
                qa_assessment.warnings.append(
                    f"{section_name} section is too brief (< {min_length} characters)"
                )
                qa_assessment.quality_score = max(0, qa_assessment.quality_score - 5)
        
        # Check for critical terms
        critical_terms = ['allergy', 'allergic', 'emergency', 'urgent', 'critical']
        all_text = f"{soap_notes.subjective} {soap_notes.objective} {soap_notes.assessment} {soap_notes.plan}".lower()
        
        for term in critical_terms:
            if term in all_text:
                qa_assessment.critical_flags.append({
                    'type': 'critical_term',
                    'message': f'Critical term "{term}" found - requires attention',
                    'severity': 'high' if term in ['emergency', 'critical'] else 'medium'
                })
        
        # Adjust approval based on critical flags
        high_severity_flags = [
            flag for flag in qa_assessment.critical_flags 
            if flag.get('severity') == 'high'
        ]
        
        if high_severity_flags:
            qa_assessment.approved = False
            qa_assessment.recommendations.append(
                "Manual review required due to high-severity critical flags"
            )
        
        # Final approval decision
        if qa_assessment.quality_score < settings.min_quality_score:
            qa_assessment.approved = False
            qa_assessment.recommendations.append(
                f"Quality score {qa_assessment.quality_score} below minimum threshold {settings.min_quality_score}"
            )
        
        return qa_assessment
