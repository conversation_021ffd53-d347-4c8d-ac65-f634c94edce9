"""
Audio processing service
"""
import os
import tempfile
import logging
import warnings
from typing import Optional

from fastapi import UploadFile, HTTPException

from config.settings import settings
from models.schemas import TranscriptionResult

# Import Whisper
import whisper

logger = logging.getLogger(__name__)

class AudioService:
    """Service for audio processing and transcription"""

    def __init__(self):
        self.whisper_model = None
        self._load_whisper_model()
    
    def _load_whisper_model(self):
        """Load Whisper model for transcription"""
        try:
            logger.info(f"Loading Whisper model: {settings.whisper_model}")

            # Suppress the FutureWarning about torch.load weights_only parameter
            # This warning comes from whisper's internal use of torch.load
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore",
                                      message="You are using `torch.load` with `weights_only=False`",
                                      category=FutureWarning)
                self.whisper_model = whisper.load_model(settings.whisper_model)

            logger.info("Whisper model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load Whisper model: {e}")
            raise RuntimeError(f"Could not load Whisper model: {e}")
    
    async def transcribe_audio(self, audio_file: UploadFile) -> TranscriptionResult:
        """
        Transcribe audio file to text using Whisper

        Args:
            audio_file: Uploaded audio file

        Returns:
            TranscriptionResult with text and metadata
        """
        if not self.whisper_model:
            raise HTTPException(status_code=500, detail="Whisper model not loaded")
        
        # Validate file
        self._validate_audio_file(audio_file)
        
        temp_file_path = None
        try:
            # Save uploaded file temporarily
            temp_file_path = await self._save_temp_file(audio_file)
            
            # Transcribe using Whisper
            logger.info(f"Transcribing audio file: {audio_file.filename}")
            result = self.whisper_model.transcribe(
                temp_file_path,
                language="en",
                task="transcribe"
            )
            
            transcription_result = TranscriptionResult(
                text=result["text"].strip(),
                confidence=self._calculate_confidence(result),
                language=result.get("language", "en"),
                duration=result.get("duration", 0.0)
            )
            
            logger.info(f"Transcription completed - Duration: {transcription_result.duration:.2f}s, "
                       f"Confidence: {transcription_result.confidence:.2f}")
            
            return transcription_result
            
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            raise HTTPException(status_code=500, detail=f"Transcription failed: {str(e)}")
        
        finally:
            # Clean up temporary file
            if temp_file_path and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
    
    def _validate_audio_file(self, audio_file: UploadFile):
        """Validate audio file format and size"""
        # Check file extension
        if not audio_file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        file_ext = os.path.splitext(audio_file.filename)[1].lower()
        supported_formats = [".wav", ".mp3", ".m4a", ".flac", ".ogg"]
        
        if file_ext not in supported_formats:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported audio format. Supported: {supported_formats}"
            )
        
        # Check file size (approximate)
        if hasattr(audio_file, 'size') and audio_file.size:
            size_mb = audio_file.size / (1024 * 1024)
            if size_mb > settings.max_audio_size_mb:
                raise HTTPException(
                    status_code=400,
                    detail=f"File too large. Maximum size: {settings.max_audio_size_mb}MB"
                )
    
    async def _save_temp_file(self, audio_file: UploadFile) -> str:
        """Save uploaded file to temporary location"""
        file_ext = os.path.splitext(audio_file.filename)[1]
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            content = await audio_file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        # Reset file pointer for potential reuse
        await audio_file.seek(0)
        
        return temp_file_path
    
    def _calculate_confidence(self, whisper_result: dict) -> float:
        """
        Calculate confidence score from Whisper result
        
        Whisper doesn't provide direct confidence scores,
        so we estimate based on available metrics
        """
        # Use segments if available for more accurate confidence
        if "segments" in whisper_result:
            segments = whisper_result["segments"]
            if segments:
                # Average confidence from segments (if available)
                confidences = []
                for segment in segments:
                    # Whisper segments may have avg_logprob
                    if "avg_logprob" in segment:
                        # Convert log probability to confidence (0-1)
                        confidence = min(1.0, max(0.0, (segment["avg_logprob"] + 1.0)))
                        confidences.append(confidence)
                
                if confidences:
                    return sum(confidences) / len(confidences)
        
        # Fallback: estimate based on text characteristics
        text = whisper_result.get("text", "")
        if not text.strip():
            return 0.0
        
        # Simple heuristic: longer, more structured text = higher confidence
        text_length = len(text.strip())
        word_count = len(text.split())
        
        if word_count == 0:
            return 0.0
        
        # Base confidence
        confidence = 0.7
        
        # Adjust based on text characteristics
        if text_length > 100:
            confidence += 0.1
        if word_count > 20:
            confidence += 0.1
        if any(char in text for char in '.!?'):
            confidence += 0.05  # Proper punctuation
        
        return min(1.0, confidence)
    
    def get_model_info(self) -> dict:
        """Get information about the loaded Whisper model"""
        if not self.whisper_model:
            return {"status": "not_loaded"}

        return {
            "status": "loaded",
            "model": settings.whisper_model,
            "max_audio_size_mb": settings.max_audio_size_mb
        }
