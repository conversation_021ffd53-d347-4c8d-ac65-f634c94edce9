"""
Application settings and configuration
"""
import os
from typing import List, Optional, Dict, Any
from pydantic import field_validator
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings(BaseSettings):
    """Application settings with validation"""
    
    # Application
    app_name: str = "MedScribe Instant"
    app_version: str = "1.0.0"
    debug: bool = False
    environment: str = "development"
    
    # Server
    host: str = "0.0.0.0"
    port: int = 8000
    allowed_origins: List[str] = ["*"]
    
    # OpenAI
    openai_api_key: str
    openai_model: str = "gpt-4"
    openai_temperature: float = 0.3
    openai_max_tokens: int = 2000
    
    # Supabase
    supabase_url: str
    supabase_anon_key: str
    
    # Whisper
    whisper_model: str = "base"
    max_audio_size_mb: int = 50
    

    
    # Security
    secret_key: str = "change-this-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Logging
    log_level: str = "INFO"
    log_file: str = "medscribe.log"
    
    # Processing
    background_task_timeout: int = 300
    max_concurrent_sessions: int = 10
    
    # Quality thresholds
    min_transcription_confidence: float = 0.7
    min_quality_score: int = 70

    # RAG Configuration
    embedding_model: str = "text-embedding-3-small"
    embedding_dimension: int = 1536
    max_chunk_size: int = 1000
    chunk_overlap: int = 200
    default_similarity_threshold: float = 0.7
    default_max_results: int = 5

    # Test mode
    test_mode: bool = False
    
    @field_validator('openai_api_key')
    @classmethod
    def validate_openai_key(cls, v, info):
        # Skip validation in test mode
        if info.data.get('test_mode', False):
            return v
        if not v or v == "your_openai_api_key_here":
            raise ValueError("OpenAI API key must be provided")
        return v

    @field_validator('supabase_url')
    @classmethod
    def validate_supabase_url(cls, v, info):
        # Skip validation in test mode
        if info.data.get('test_mode', False):
            return v
        if not v or v == "https://your-project.supabase.co":
            raise ValueError("Supabase URL must be provided")
        return v

    @field_validator('supabase_anon_key')
    @classmethod
    def validate_supabase_key(cls, v, info):
        # Skip validation in test mode
        if info.data.get('test_mode', False):
            return v
        if not v or v == "your_supabase_anon_key_here":
            raise ValueError("Supabase anonymous key must be provided")
        return v

    @field_validator('whisper_model')
    @classmethod
    def validate_whisper_model(cls, v):
        valid_models = ["tiny", "base", "small", "medium", "large"]
        if v not in valid_models:
            raise ValueError(f"Whisper model must be one of: {valid_models}")
        return v

    @field_validator('environment')
    @classmethod
    def validate_environment(cls, v):
        valid_envs = ["development", "staging", "production"]
        if v not in valid_envs:
            raise ValueError(f"Environment must be one of: {valid_envs}")
        return v
    
    model_config = {
        "env_file": ".env",
        "case_sensitive": False
    }

# Global settings instance
settings = Settings()

# Specialty configurations
SPECIALTY_CONFIGS = {
    "cardiology": {
        "focus_areas": ["cardiac history", "ECG findings", "echo results"],
        "required_sections": ["chest pain assessment", "cardiac risk factors"],
        "icd_codes_prefix": ["I"],
        "common_medications": ["metoprolol", "lisinopril", "atorvastatin"]
    },
    "dermatology": {
        "focus_areas": ["lesion description", "distribution patterns"],
        "required_sections": ["skin examination", "dermatoscopy findings"],
        "icd_codes_prefix": ["L"],
        "common_medications": ["topical steroids", "antifungals"]
    },
    "orthopedics": {
        "focus_areas": ["range of motion", "strength testing"],
        "required_sections": ["musculoskeletal examination"],
        "icd_codes_prefix": ["M", "S"],
        "common_medications": ["NSAIDs", "muscle relaxants"]
    },
    "neurology": {
        "focus_areas": ["neurological examination", "cognitive assessment"],
        "required_sections": ["mental status", "cranial nerves"],
        "icd_codes_prefix": ["G"],
        "common_medications": ["anticonvulsants", "dopamine agonists"]
    },
    "pediatrics": {
        "focus_areas": ["growth parameters", "developmental milestones"],
        "required_sections": ["growth chart", "developmental screening"],
        "icd_codes_prefix": ["P", "Q"],
        "common_medications": ["pediatric formulations", "vaccines"]
    },
    "general": {
        "focus_areas": ["chief complaint", "review of systems"],
        "required_sections": ["history of present illness"],
        "icd_codes_prefix": [],
        "common_medications": []
    }
}

# Event type configurations for dual-role RAG system
DOCTOR_EVENT_TYPES = [
    'schedule_slots',           # Doctor availability
    'appointment_booked',       # Booked appointments
    'appointment_scheduled',    # Scheduled appointments (new)
    'medication_prescription',  # Prescribed medications
    'medication_prescribed',    # Prescribed medications (new)
    'prescription_issued',      # Prescriptions issued (new)
    'treatment_plan_created',   # Treatment plans created (new)
    'medical_goal_set',         # Medical goals set (new)
    'soap_note_created',        # SOAP notes created (new)
    'soap_note_shared_out',     # SOAP notes shared to others (new)
    'soap_note_shared_in',      # SOAP notes received from others (new)
    'soap_note_updated',        # SOAP notes updated (new)
    'soap_note_accepted',       # SOAP notes accepted (new)
    'soap_note_rejected',       # SOAP notes rejected (new)
    'soap_note_reviewed',       # SOAP notes reviewed (new)
    'soap_note_commented',      # SOAP notes commented (new)
    'patient_registered',       # New patient registrations
    'consultation_notes',       # Doctor's consultation notes
    'treatment_plan',          # Treatment plans created (legacy)
    'referral_sent',           # Referrals to other doctors
    'lab_order',               # Lab orders placed
    'diagnosis_made',          # Diagnoses made
    'procedure_scheduled',     # Procedures scheduled
    'follow_up_scheduled',     # Follow-up appointments
    'patient_discharged'       # Patient discharge notes
]

PATIENT_EVENT_TYPES = [
    'soap_note_shared',        # Shared SOAP notes
    'soap_note_created',       # SOAP notes created (new)
    'soap_note_updated',       # SOAP notes updated (new)
    'soap_note_accepted',      # SOAP notes accepted (new)
    'soap_note_rejected',      # SOAP notes rejected (new)
    'soap_note_reviewed',      # SOAP notes reviewed (new)
    'soap_note_commented',     # SOAP notes commented (new)
    'appointment_requested',   # Appointment requests
    'appointment_scheduled',   # Scheduled appointments (new)
    'reschedule_requested',    # Reschedule requests
    'medication_received',     # Received medications
    'medication_prescribed',   # Prescribed medications (new)
    'prescription_issued',     # Prescriptions issued (new)
    'treatment_plan_created',  # Treatment plans created (new)
    'medical_goal_set',        # Medical goals set (new)
    'symptom_report',          # Patient-reported symptoms
    'medication_adherence',    # Medication compliance reports
    'side_effect_report',      # Side effect reports
    'pain_scale_report',       # Pain level reports
    'vital_signs_home',        # Home vital sign measurements
    'question_for_doctor',     # Questions for the doctor
    'appointment_feedback',    # Feedback after appointments
    'treatment_response'       # Response to treatment
]

# Legacy patient event types for backward compatibility
LEGACY_PATIENT_EVENT_TYPES = [
    'soap', 'conversation', 'medication', 'appointment',
    'diagnosis', 'treatment', 'lab_results', 'imaging',
    'consultation', 'discharge_summary', 'progress_note'
]

# Response type configurations
RESPONSE_TYPES = {
    'list': 'Structured list response',
    'text': 'Plain text response',
    'time_slots': 'Available time slots',
    'medication': 'Medication information',
    'appointment_status': 'Appointment status information',
    'soap_note': 'SOAP note format',
    'reschedule_request': 'Reschedule request details',
    'patient_list': 'List of patients',
    'schedule_overview': 'Schedule overview',
    'symptom_analysis': 'Symptom analysis',
    'treatment_progress': 'Treatment progress report',
    'medication_schedule': 'Medication schedule',
    'vital_signs': 'Vital signs data'
}

def get_specialty_config(specialty: str) -> dict:
    """Get configuration for a specific medical specialty"""
    return SPECIALTY_CONFIGS.get(specialty.lower(), SPECIALTY_CONFIGS["general"])

def get_valid_event_types(role_type: str) -> List[str]:
    """Get valid event types for a specific role"""
    if role_type == 'doctor':
        return DOCTOR_EVENT_TYPES
    elif role_type == 'patient':
        return PATIENT_EVENT_TYPES + LEGACY_PATIENT_EVENT_TYPES
    else:
        return []

def is_valid_event_type(role_type: str, event_type: str) -> bool:
    """Check if an event type is valid for a specific role"""
    return event_type in get_valid_event_types(role_type)

# RAG System Configuration
RAG_CONFIG = {
    'embedding_model': 'text-embedding-3-small',
    'embedding_dimension': 1536,
    'chunk_size': 1000,
    'chunk_overlap': 200,
    'default_similarity_threshold': 0.7,
    'default_max_results': 5,
    'max_query_length': 1000,
    'max_content_length': 10000,
    'enable_cross_role_search': True,  # Allow doctors to search patient data
    'enable_structured_responses': True,
    'cache_embeddings': True,
    'cache_ttl_seconds': 3600
}

# Database Configuration for Dual-Role System
DATABASE_CONFIG = {
    'main_table': 'medical_knowledge_base',
    'legacy_table': 'patient_knowledge_base',
    'search_sessions_table': 'rag_search_sessions',
    'enable_legacy_support': True,
    'migration_batch_size': 100,
    'max_connections': 10
}

# Role-based permissions
ROLE_PERMISSIONS = {
    'doctor': {
        'can_access_patient_data': True,
        'can_cross_role_search': True,
        'can_view_all_event_types': True,
        'max_results_limit': 20,
        'rate_limit_per_minute': 100
    },
    'patient': {
        'can_access_patient_data': False,  # Only their own data
        'can_cross_role_search': False,
        'can_view_all_event_types': False,
        'max_results_limit': 10,
        'rate_limit_per_minute': 50
    }
}

# Structured response configuration
STRUCTURED_RESPONSE_CONFIG = {
    'enable_auto_detection': True,
    'default_response_type': 'text',
    'include_confidence_scores': True,
    'include_source_attribution': True,
    'max_structured_data_size': 5000  # bytes
}

def get_role_permissions(role_type: str) -> Dict[str, Any]:
    """Get permissions for a specific role"""
    return ROLE_PERMISSIONS.get(role_type, ROLE_PERMISSIONS['patient'])

def get_max_results_for_role(role_type: str) -> int:
    """Get maximum results limit for a role"""
    permissions = get_role_permissions(role_type)
    return permissions.get('max_results_limit', 5)

def can_cross_role_search(role_type: str) -> bool:
    """Check if a role can perform cross-role searches"""
    permissions = get_role_permissions(role_type)
    return permissions.get('can_cross_role_search', False)
