"""
RAG API endpoints for patient-specific knowledge storage and retrieval
"""
import logging
from typing import Optional, List

from fastapi import APIRouter, HTTPException, Form
from pydantic import BaseModel, Field

from services.rag_service import RAGService

logger = logging.getLogger(__name__)

router = APIRouter()

# RAG service will be initialized lazily
rag_service = None

def get_rag_service():
    """Get RAG service instance, initializing if needed"""
    global rag_service
    if rag_service is None:
        rag_service = RAGService()
    return rag_service

# Request/Response Models
class EmbedRequest(BaseModel):
    """Request model for embedding medical data - supports both doctors and patients"""
    # Role identification - exactly one must be provided
    doctor_id: Optional[str] = Field(None, description="Doctor identifier")
    patient_id: Optional[str] = Field(None, description="Patient identifier")

    event_type: str = Field(..., description="Type of event (varies by role)")
    data: str = Field(..., description="Text data to store and embed")
    metadata: Optional[dict] = Field(None, description="Additional metadata")

    def get_role_info(self) -> tuple[str, str]:
        """Get role type and role ID from the request"""
        if self.doctor_id and self.patient_id:
            raise ValueError("Cannot specify both doctor_id and patient_id")
        if not self.doctor_id and not self.patient_id:
            raise ValueError("Must specify either doctor_id or patient_id")

        if self.doctor_id:
            return ("doctor", self.doctor_id)
        else:
            return ("patient", self.patient_id)

class DoctorSearchRequest(BaseModel):
    """Request model for doctor-specific searches"""
    doctor_id: str = Field(..., description="Doctor identifier")
    query: str = Field(..., description="Search query")
    similarity_threshold: Optional[float] = Field(0.7, description="Minimum similarity threshold (0-1)")
    max_results: Optional[int] = Field(5, description="Maximum number of results")
    event_types: Optional[List[str]] = Field(None, description="Filter by specific event types")
    include_context: Optional[bool] = Field(True, description="Include retrieved context in response")

class PatientSearchRequest(BaseModel):
    """Request model for patient-specific searches"""
    patient_id: str = Field(..., description="Patient identifier")
    query: str = Field(..., description="Search query")
    similarity_threshold: Optional[float] = Field(0.7, description="Minimum similarity threshold (0-1)")
    max_results: Optional[int] = Field(5, description="Maximum number of results")
    event_types: Optional[List[str]] = Field(None, description="Filter by specific event types")
    include_context: Optional[bool] = Field(True, description="Include retrieved context in response")

# Legacy model for backward compatibility
class SearchRequest(BaseModel):
    """Legacy request model for searching patient knowledge (backward compatibility)"""
    patient_id: str = Field(..., description="Patient identifier")
    query: str = Field(..., description="Search query")
    similarity_threshold: Optional[float] = Field(0.2, description="Minimum similarity threshold (0-1)")
    max_results: Optional[int] = Field(5, description="Maximum number of results")
    event_types: Optional[List[str]] = Field(None, description="Filter by specific event types")
    include_context: Optional[bool] = Field(True, description="Include retrieved context in response")

# Structured response types
class StructuredResponse(BaseModel):
    """Structured response for different types of queries"""
    type: str = Field(..., description="Response type (list, text, time_slots, medication, etc.)")
    summary: str = Field(..., description="Brief summary of the response")
    data: dict = Field(..., description="Structured data specific to the response type")
    timestamp: Optional[str] = Field(None, description="Response timestamp")

class EmbedResponse(BaseModel):
    """Response model for embedding operations"""
    success: bool
    message: Optional[str] = None
    error: Optional[str] = None
    role_type: str = Field(..., description="Role type (doctor or patient)")
    role_id: str = Field(..., description="Role identifier")
    event_type: str
    data_length: Optional[int] = None
    processing_time_seconds: Optional[float] = None
    knowledge_base_stats: Optional[dict] = None
    stored_at: Optional[str] = None

class SearchResponse(BaseModel):
    """Response model for search operations"""
    success: bool
    message: Optional[str] = None
    error: Optional[str] = None
    role_type: str = Field(..., description="Role type (doctor or patient)")
    role_id: str = Field(..., description="Role identifier")
    query: str
    response: Optional[str] = None
    structured_response: Optional[StructuredResponse] = None
    relevant_documents_count: Optional[int] = None
    relevant_documents: Optional[List[dict]] = None
    context_used: Optional[bool] = None
    similarity_threshold: Optional[float] = None
    max_results: Optional[int] = None
    processing_time_seconds: Optional[float] = None
    generated_at: Optional[str] = None

# Legacy response model for backward compatibility
class LegacySearchResponse(BaseModel):
    """Legacy response model for patient search (backward compatibility)"""
    success: bool
    message: Optional[str] = None
    error: Optional[str] = None
    patient_id: str
    query: str
    response: Optional[str] = None
    relevant_documents_count: Optional[int] = None
    relevant_documents: Optional[List[dict]] = None
    context_used: Optional[bool] = None
    similarity_threshold: Optional[float] = None
    max_results: Optional[int] = None
    processing_time_seconds: Optional[float] = None
    generated_at: Optional[str] = None

@router.post("/embed", response_model=EmbedResponse)
async def embed_medical_data(request: EmbedRequest):
    """
    Store medical data with embeddings for RAG retrieval (supports both doctors and patients)

    This endpoint processes medical data (SOAP notes, conversations, medications, schedules, etc.)
    and stores it in the vector database for semantic search and retrieval.

    Args:
        request: EmbedRequest containing role information, event_type, data, and optional metadata

    Returns:
        EmbedResponse with storage status and details
    """
    try:
        # Get role information from request
        try:
            role_type, role_id = request.get_role_info()
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))

        logger.info(f"Embedding data for {role_type} {role_id}, event type: {request.event_type}")

        # Import validation functions
        from config.settings import is_valid_event_type, get_valid_event_types

        # Validate event type for the specific role
        if not is_valid_event_type(role_type, request.event_type):
            valid_types = get_valid_event_types(role_type)
            logger.warning(f"Invalid event type '{request.event_type}' for {role_type}. Valid types: {valid_types}")
            # Don't reject, just log warning - allow flexibility for new event types

        # Store medical data using the new dual-role method
        result = await get_rag_service().store_medical_data(
            role_type=role_type,
            role_id=role_id,
            event_type=request.event_type,
            data=request.data,
            metadata=request.metadata
        )
        print(result)

        if result['success']:
            logger.info(f"Successfully embedded data for {role_type} {role_id}")
            response = EmbedResponse(**result)
            print(f"EMBED API RESPONSE TO CLIENT: {response.model_dump()}")
            return response
        else:
            logger.error(f"Failed to embed data for {role_type} {role_id}: {result.get('error')}")
            raise HTTPException(status_code=500, detail=result.get('error', 'Embedding failed'))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Embedding endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=f"Embedding failed: {str(e)}")

@router.post("/search/doctor", response_model=SearchResponse)
async def search_doctor_knowledge(request: DoctorSearchRequest):
    """
    Search doctor-specific knowledge base using semantic similarity

    This endpoint performs semantic search on the doctor's stored medical data
    and generates contextual responses using retrieved information.

    Args:
        request: DoctorSearchRequest containing doctor_id, query, and search parameters

    Returns:
        SearchResponse with generated answer and relevant context
    """
    try:
        logger.info(f"Searching knowledge for doctor {request.doctor_id}, query: {request.query[:100]}...")

        # Validate similarity threshold
        if request.similarity_threshold and (request.similarity_threshold < 0 or request.similarity_threshold > 1):
            raise HTTPException(status_code=400, detail="Similarity threshold must be between 0 and 1")

        # Validate max results
        if request.max_results and (request.max_results < 1 or request.max_results > 20):
            raise HTTPException(status_code=400, detail="Max results must be between 1 and 20")

        # Query doctor knowledge with cross-role search capability
        result = await get_rag_service().query_medical_knowledge(
            role_type='doctor',
            role_id=request.doctor_id,
            query=request.query,
            similarity_threshold=request.similarity_threshold,
            max_results=request.max_results,
            event_types=request.event_types,
            include_context=request.include_context,
            cross_role_search=True  # Doctors can search across patient data too
        )
        print(result)

        if result['success']:
            logger.info(f"Successfully searched knowledge for doctor {request.doctor_id}")
            response = SearchResponse(**result)
            print(f"DOCTOR SEARCH API RESPONSE TO CLIENT: {response.model_dump()}")
            return response
        else:
            logger.error(f"Failed to search knowledge for doctor {request.doctor_id}: {result.get('error')}")
            raise HTTPException(status_code=500, detail=result.get('error', 'Search failed'))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Doctor search endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=f"Doctor search failed: {str(e)}")

@router.post("/search/patient", response_model=SearchResponse)
async def search_patient_knowledge(request: PatientSearchRequest):
    """
    Search patient-specific knowledge base using semantic similarity

    This endpoint performs semantic search on the patient's stored medical data
    and generates contextual responses using retrieved information.

    Args:
        request: PatientSearchRequest containing patient_id, query, and search parameters

    Returns:
        SearchResponse with generated answer and relevant context
    """
    try:
        logger.info(f"Searching knowledge for patient {request.patient_id}, query: {request.query[:100]}...")

        # Validate similarity threshold
        if request.similarity_threshold and (request.similarity_threshold < 0 or request.similarity_threshold > 1):
            raise HTTPException(status_code=400, detail="Similarity threshold must be between 0 and 1")

        # Validate max results
        if request.max_results and (request.max_results < 1 or request.max_results > 20):
            raise HTTPException(status_code=400, detail="Max results must be between 1 and 20")

        # Query patient knowledge
        result = await get_rag_service().query_medical_knowledge(
            role_type='patient',
            role_id=request.patient_id,
            query=request.query,
            similarity_threshold=request.similarity_threshold,
            max_results=request.max_results,
            event_types=request.event_types,
            include_context=request.include_context,
            cross_role_search=False  # Patients only search their own data
        )
        print(result)

        if result['success']:
            logger.info(f"Successfully searched knowledge for patient {request.patient_id}")
            response = SearchResponse(**result)
            print(f"PATIENT SEARCH API RESPONSE TO CLIENT: {response.model_dump()}")
            return response
        else:
            logger.error(f"Failed to search knowledge for patient {request.patient_id}: {result.get('error')}")
            raise HTTPException(status_code=500, detail=result.get('error', 'Search failed'))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Patient search endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=f"Patient search failed: {str(e)}")

@router.post("/search", response_model=LegacySearchResponse)
async def search_patient_knowledge_legacy(request: SearchRequest):
    """
    Legacy search endpoint for patient knowledge (backward compatibility)

    This endpoint maintains backward compatibility for existing integrations
    while internally using the new dual-role architecture.

    Args:
        request: SearchRequest containing patient_id, query, and search parameters

    Returns:
        LegacySearchResponse with generated answer and relevant context
    """
    try:
        logger.info(f"Legacy search for patient {request.patient_id}, query: {request.query[:100]}...")

        # Validate similarity threshold
        if request.similarity_threshold and (request.similarity_threshold < 0 or request.similarity_threshold > 1):
            raise HTTPException(status_code=400, detail="Similarity threshold must be between 0 and 1")

        # Validate max results
        if request.max_results and (request.max_results < 1 or request.max_results > 20):
            raise HTTPException(status_code=400, detail="Max results must be between 1 and 20")

        # Query patient knowledge using legacy method
        result = await get_rag_service().query_patient_knowledge(
            patient_id=request.patient_id,
            query=request.query,
            similarity_threshold=request.similarity_threshold,
            max_results=request.max_results,
            event_types=request.event_types,
            include_context=request.include_context
        )
        print(result)

        if result['success']:
            logger.info(f"Successfully searched knowledge for patient {request.patient_id}")
            response = LegacySearchResponse(**result)
            print(f"LEGACY SEARCH API RESPONSE TO CLIENT: {response.model_dump()}")
            return response
        else:
            logger.error(f"Failed to search knowledge for patient {request.patient_id}: {result.get('error')}")
            raise HTTPException(status_code=500, detail=result.get('error', 'Search failed'))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Legacy search endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@router.get("/doctor/{doctor_id}/summary")
async def get_doctor_knowledge_summary(doctor_id: str):
    """
    Get summary of doctor's knowledge base

    Args:
        doctor_id: Doctor identifier

    Returns:
        Knowledge base statistics and summary
    """
    try:
        logger.info(f"Getting knowledge summary for doctor {doctor_id}")

        result = await get_rag_service().get_medical_knowledge_summary('doctor', doctor_id)

        if result['success']:
            print(f"DOCTOR SUMMARY API RESPONSE TO CLIENT: {result}")
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get('error', 'Failed to get summary'))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Doctor summary endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=f"Summary failed: {str(e)}")

@router.get("/patient/{patient_id}/summary")
async def get_patient_knowledge_summary(patient_id: str):
    """
    Get summary of patient's knowledge base (legacy endpoint with dual-role support)

    Args:
        patient_id: Patient identifier

    Returns:
        Knowledge base statistics and summary
    """
    try:
        logger.info(f"Getting knowledge summary for patient {patient_id}")

        result = await get_rag_service().get_medical_knowledge_summary('patient', patient_id)

        if result['success']:
            # Convert to legacy format for backward compatibility
            legacy_result = {
                'success': result['success'],
                'patient_id': result['role_id'],
                'knowledge_base_summary': result['knowledge_base_summary'],
                'generated_at': result['generated_at']
            }
            print(f"PATIENT SUMMARY API RESPONSE TO CLIENT: {legacy_result}")
            return legacy_result
        else:
            raise HTTPException(status_code=500, detail=result.get('error', 'Failed to get summary'))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Patient summary endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=f"Summary failed: {str(e)}")

# Alternative form-based endpoints for simpler integration
@router.post("/embed-form")
async def embed_patient_data_form(
    patient_id: str = Form(..., description="Patient identifier"),
    event_type: str = Form(..., description="Type of event"),
    data: str = Form(..., description="Text data to store"),
    metadata: Optional[str] = Form(None, description="JSON metadata (optional)")
):
    """
    Form-based endpoint for embedding patient data
    
    Alternative to JSON endpoint for easier integration with form submissions
    """
    try:
        # Parse metadata if provided
        parsed_metadata = None
        if metadata:
            import json
            try:
                parsed_metadata = json.loads(metadata)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid JSON in metadata field")
        
        # Create request object
        request = EmbedRequest(
            patient_id=patient_id,
            event_type=event_type,
            data=data,
            metadata=parsed_metadata
        )
        print(request)
        
        # Use the main embed endpoint
        return await embed_patient_data(request)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Form embed endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=f"Form embedding failed: {str(e)}")

@router.post("/search-form")
async def search_patient_knowledge_form(
    patient_id: str = Form(..., description="Patient identifier"),
    query: str = Form(..., description="Search query"),
    similarity_threshold: Optional[float] = Form(0.2, description="Similarity threshold"),
    max_results: Optional[int] = Form(5, description="Maximum results"),
    event_types: Optional[str] = Form(None, description="Comma-separated event types"),
    include_context: Optional[bool] = Form(True, description="Include context")
):
    """
    Form-based endpoint for searching patient knowledge
    
    Alternative to JSON endpoint for easier integration with form submissions
    """
    try:
        # Parse event types if provided
        parsed_event_types = None
        if event_types:
            parsed_event_types = [et.strip() for et in event_types.split(',') if et.strip()]
        
        # Create request object
        request = SearchRequest(
            patient_id=patient_id,
            query=query,
            similarity_threshold=similarity_threshold,
            max_results=max_results,
            event_types=parsed_event_types,
            include_context=include_context
        )
        print(request)
        
        # Use the main search endpoint
        return await search_patient_knowledge(request)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Form search endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=f"Form search failed: {str(e)}")

@router.get("/health")
async def rag_health_check():
    """Health check for RAG service"""
    try:
        # Basic health check
        return {
            "status": "healthy",
            "service": "RAG",
            "timestamp": "2024-01-01T00:00:00Z",
            "endpoints": [
                "/rag/embed",
                "/rag/search/doctor",
                "/rag/search/patient",
                "/rag/search",  # Legacy endpoint
                "/rag/doctor/{doctor_id}/summary",
                "/rag/patient/{patient_id}/summary",
                "/rag/embed-form",
                "/rag/search-form"
            ],
            "features": [
                "Dual-role support (doctors and patients)",
                "Structured response types",
                "Cross-role search for doctors",
                "Backward compatibility",
                "Event type validation"
            ]
        }
    except Exception as e:
        logger.error(f"RAG health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")
