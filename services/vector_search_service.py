"""
Vector Search Service for RAG functionality
Handles vector storage and similarity search using Supabase + pgvector
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timezone

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

from config.settings import settings
from database.connection import get_database
from services.embedding_service import EmbeddingService

logger = logging.getLogger(__name__)

class VectorSearchService:
    """Service for vector storage and similarity search"""
    
    def __init__(self):
        self.supabase = None
        self.embedding_service = EmbeddingService()
        self.default_similarity_threshold = 0.2  # Lowered for better medical content matching
        self.default_max_results = 5

        logger.info("Vector search service initialized")

    def _get_database(self):
        """Get database connection, initializing if needed"""
        if self.supabase is None:
            self.supabase = get_database()
        return self.supabase
    
    async def store_medical_embeddings(
        self,
        role_type: str,
        role_id: str,
        event_type: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Store medical data with embeddings in the knowledge base (supports both doctors and patients)

        Args:
            role_type: Role type ('doctor' or 'patient')
            role_id: Role identifier (doctor_id or patient_id)
            event_type: Type of event (varies by role)
            content: Text content to store
            metadata: Additional metadata

        Returns:
            Success status
        """
        try:
            # Process content into chunks with embeddings
            processed_chunks = await self.embedding_service.embed_medical_data(
                role_type, role_id, event_type, content, metadata
            )

            if not processed_chunks:
                logger.warning(f"No chunks generated for {role_type} {role_id}")
                return False

            # Store chunks in the new medical_knowledge_base table
            stored_count = 0
            for chunk in processed_chunks:
                try:
                    result = self._get_database().table('medical_knowledge_base').insert({
                        'role_type': chunk['role_type'],
                        'role_id': chunk['role_id'],
                        'event_type': chunk['event_type'],
                        'content': chunk['content'],
                        'content_chunk': chunk['content_chunk'],
                        'embedding': chunk['embedding'],
                        'metadata': chunk['metadata']
                    }).execute()

                    if result.data:
                        stored_count += 1
                    else:
                        logger.warning(f"No data returned when storing chunk for {role_type} {role_id}")

                except Exception as e:
                    error_message = str(e)
                    # Handle duplicate content gracefully
                    if 'duplicate key value violates unique constraint "unique_role_content"' in error_message:
                        logger.info(f"Duplicate content detected for {role_type} {role_id}, skipping chunk")
                        # Consider this as successfully handled
                        stored_count += 1
                        continue
                    else:
                        logger.error(f"Database error storing chunk: {e}")
                        return False

            if stored_count > 0:
                logger.info(f"Successfully stored {stored_count}/{len(processed_chunks)} chunks for {role_type} {role_id}")
                return True
            else:
                logger.warning(f"No chunks were stored for {role_type} {role_id}")
                return False

        except Exception as e:
            logger.error(f"Failed to store medical embeddings: {e}")
            return False

    async def store_patient_embeddings(
        self,
        patient_id: str,
        event_type: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Legacy method for storing patient data (backward compatibility)

        Args:
            patient_id: Patient identifier
            event_type: Type of event (soap, conversation, medication, etc.)
            content: Text content to store
            metadata: Additional metadata

        Returns:
            Success status
        """
        # Use the new dual-role method
        success = await self.store_medical_embeddings('patient', patient_id, event_type, content, metadata)

        if success:
            # Also store in legacy table for backward compatibility
            try:
                processed_chunks = await self.embedding_service.embed_patient_data(
                    patient_id, event_type, content, metadata
                )

                if not processed_chunks:
                    logger.warning(f"No chunks generated for patient {patient_id}")
                    return False

                # Store chunks in legacy database table
                for chunk in processed_chunks:
                    try:
                        result = self._get_database().table('patient_knowledge_base').insert({
                            'patient_id': chunk['patient_id'],
                            'event_type': chunk['event_type'],
                            'content': chunk['content'],
                            'content_chunk': chunk['content_chunk'],
                            'embedding': chunk['embedding'],
                            'metadata': chunk['metadata']
                        }).execute()

                        if not result.data:
                            logger.error(f"Failed to insert chunk for patient {patient_id}")

                    except Exception as e:
                        logger.error(f"Failed to insert chunk: {e}")
                        continue

                logger.info(f"Stored {len(processed_chunks)} chunks for patient {patient_id} (legacy table)")

            except Exception as e:
                logger.error(f"Failed to store in legacy table: {e}")

        return success

    async def search_medical_knowledge(
        self,
        role_type: str,
        role_id: str,
        query: str,
        similarity_threshold: float = None,
        max_results: int = None,
        event_types: Optional[List[str]] = None,
        cross_role_search: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Search medical knowledge base using semantic similarity (supports both doctors and patients)

        Args:
            role_type: Role type ('doctor' or 'patient')
            role_id: Role identifier (doctor_id or patient_id)
            query: Search query
            similarity_threshold: Minimum similarity score (0-1)
            max_results: Maximum number of results
            event_types: Filter by specific event types
            cross_role_search: Whether to search across roles (for doctors accessing patient data)

        Returns:
            List of relevant knowledge base entries with similarity scores
        """
        try:
            # Set defaults
            similarity_threshold = similarity_threshold or self.default_similarity_threshold
            max_results = max_results or self.default_max_results

            # Generate query embedding
            query_embedding = await self.embedding_service.generate_embedding(query)

            if not query_embedding or all(x == 0.0 for x in query_embedding):
                logger.warning("Failed to generate query embedding")
                return []

            # Search using vector similarity
            results = await self._vector_similarity_search_medical(
                role_type, role_id, query_embedding, similarity_threshold,
                max_results, event_types, cross_role_search
            )

            # Log search session
            await self._log_search_session_medical(
                role_type, role_id, query, query_embedding, results,
                similarity_threshold, max_results
            )

            logger.info(f"Found {len(results)} relevant chunks for {role_type} {role_id}")
            return results

        except Exception as e:
            logger.error(f"Failed to search medical knowledge: {e}")
            return []

    async def search_patient_knowledge(
        self, 
        patient_id: str, 
        query: str, 
        similarity_threshold: float = None,
        max_results: int = None,
        event_types: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Legacy method for patient knowledge search (backward compatibility)

        Args:
            patient_id: Patient identifier
            query: Search query
            similarity_threshold: Minimum similarity score (0-1)
            max_results: Maximum number of results
            event_types: Filter by specific event types

        Returns:
            List of relevant knowledge base entries with similarity scores
        """
        # Use the new dual-role search method
        return await self.search_medical_knowledge(
            'patient', patient_id, query, similarity_threshold,
            max_results, event_types, cross_role_search=False
        )

    async def _vector_similarity_search_medical(
        self,
        role_type: str,
        role_id: str,
        query_embedding: List[float],
        similarity_threshold: float,
        max_results: int,
        event_types: Optional[List[str]] = None,
        cross_role_search: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Perform vector similarity search using Supabase for medical knowledge base
        """
        try:
            # Build base query for new medical_knowledge_base table
            query_builder = self._get_database().table('medical_knowledge_base').select(
                'id, role_type, role_id, event_type, content, content_chunk, metadata, created_at, embedding'
            )

            # Apply role filtering
            if cross_role_search and role_type == 'doctor':
                # Doctors can search across both their data and patient data
                query_builder = query_builder.in_('role_type', ['doctor', 'patient'])
                # For cross-role search, we might want to filter by specific role_id or allow all
                query_builder = query_builder.eq('role_id', role_id)  # Or remove this for broader search
            else:
                # Standard role-based filtering
                query_builder = query_builder.eq('role_type', role_type).eq('role_id', role_id)

            # Add event type filter if specified
            if event_types:
                query_builder = query_builder.in_('event_type', event_types)

            # Execute query to get all relevant data
            result = query_builder.execute()

            if not result.data:
                return []

            # Calculate similarities and filter
            similar_chunks = []
            for row in result.data:
                if row['embedding']:
                    similarity = self._calculate_cosine_similarity(query_embedding, row['embedding'])
                    if similarity >= similarity_threshold:
                        similar_chunks.append({
                            'id': row['id'],
                            'role_type': row['role_type'],
                            'role_id': row['role_id'],
                            'event_type': row['event_type'],
                            'content': row['content'],
                            'content_chunk': row['content_chunk'],
                            'metadata': row['metadata'],
                            'created_at': row['created_at'],
                            'similarity_score': similarity
                        })

            # Sort by similarity and limit results
            similar_chunks.sort(key=lambda x: x['similarity_score'], reverse=True)
            return similar_chunks[:max_results]

        except Exception as e:
            logger.error(f"Vector similarity search failed: {e}")
            return []

    def _calculate_cosine_similarity(self, embedding1: List[float], embedding2) -> float:
        """Calculate cosine similarity between two embeddings."""
        try:
            import numpy as np
            import json

            # Convert embedding1 to numpy array
            vec1 = np.array(embedding1, dtype=np.float32)

            # Handle embedding2 which might be a string from database
            if isinstance(embedding2, str):
                # Try to parse as JSON array
                try:
                    embedding2_list = json.loads(embedding2)
                    vec2 = np.array(embedding2_list, dtype=np.float32)
                except:
                    # If JSON parsing fails, try to parse as a list representation
                    embedding2_list = eval(embedding2) if embedding2.startswith('[') else embedding2
                    vec2 = np.array(embedding2_list, dtype=np.float32)
            else:
                vec2 = np.array(embedding2, dtype=np.float32)

            # Calculate cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            similarity = dot_product / (norm1 * norm2)
            return float(similarity)

        except Exception as e:
            logger.error(f"Failed to calculate cosine similarity: {e}")
            return 0.0

    async def _vector_similarity_search(
        self,
        patient_id: str,
        query_embedding: List[float],
        similarity_threshold: float,
        max_results: int,
        event_types: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform vector similarity search using Supabase
        """
        try:
            # Build base query
            query_builder = self._get_database().table('patient_knowledge_base').select(
                'id, patient_id, event_type, content, content_chunk, metadata, created_at, embedding'
            ).eq('patient_id', patient_id)

            # Add event type filter if specified
            if event_types:
                query_builder = query_builder.in_('event_type', event_types)

            # Execute query to get all patient data
            result = query_builder.execute()

            if not result.data:
                return []

            # Calculate similarities manually (in production, use pgvector functions)
            similarities = []
            query_vector = np.array(query_embedding).reshape(1, -1)

            for row in result.data:
                try:
                    if row['embedding']:
                        # Handle embedding conversion - it might be stored as string or list
                        embedding = row['embedding']

                        if isinstance(embedding, str):
                            # Use eval for parsing Python literals with scientific notation
                            try:
                                # Safe eval since we control the data source
                                embedding = eval(embedding)
                            except Exception as e:
                                logger.error(f"Failed to parse embedding string for row {row.get('id')}: {e}")
                                continue

                        # Convert to numpy array and calculate similarity
                        doc_vector = np.array(embedding, dtype=float).reshape(1, -1)
                        similarity = cosine_similarity(query_vector, doc_vector)[0][0]

                        if similarity >= similarity_threshold:
                            similarities.append({
                                **row,
                                'similarity_score': float(similarity)
                            })
                except Exception as e:
                    logger.error(f"Error calculating similarity for row {row.get('id')}: {e}")
                    continue

            # Sort by similarity and limit results
            similarities.sort(key=lambda x: x['similarity_score'], reverse=True)
            return similarities[:max_results]

        except Exception as e:
            logger.error(f"Vector similarity search failed: {e}")
            return []
    
    async def _log_search_session(
        self,
        patient_id: str,
        query: str,
        query_embedding: List[float],
        results: List[Dict[str, Any]],
        similarity_threshold: float,
        max_results: int
    ):
        """Log search session for analytics and debugging"""
        try:
            search_data = {
                'patient_id': patient_id,
                'query': query,
                'query_embedding': query_embedding,
                'results': [
                    {
                        'id': r.get('id'),
                        'event_type': r.get('event_type'),
                        'similarity_score': r.get('similarity_score'),
                        'content_preview': r.get('content_chunk', '')[:100]
                    } for r in results
                ],
                'similarity_threshold': similarity_threshold,
                'max_results': max_results
            }
            
            self._get_database().table('rag_search_sessions').insert(search_data).execute()
            
        except Exception as e:
            logger.error(f"Failed to log search session: {e}")

    async def _log_search_session_medical(
        self,
        role_type: str,
        role_id: str,
        query: str,
        query_embedding: List[float],
        results: List[Dict[str, Any]],
        similarity_threshold: float,
        max_results: int
    ):
        """Log search session for medical knowledge base (dual-role support)"""
        try:
            search_data = {
                'role_type': role_type,
                'role_id': role_id,
                'query': query,
                'query_embedding': query_embedding,
                'results': [
                    {
                        'id': r.get('id'),
                        'role_type': r.get('role_type'),
                        'role_id': r.get('role_id'),
                        'event_type': r.get('event_type'),
                        'similarity_score': r.get('similarity_score'),
                        'content_preview': r.get('content_chunk', '')[:100]
                    } for r in results
                ],
                'similarity_threshold': similarity_threshold,
                'max_results': max_results
            }

            self._get_database().table('rag_search_sessions').insert(search_data).execute()

        except Exception as e:
            logger.error(f"Failed to log medical search session: {e}")

    async def get_medical_knowledge_stats(
        self,
        role_type: str,
        role_id: str
    ) -> Dict[str, Any]:
        """
        Get statistics about medical knowledge base for a specific role

        Args:
            role_type: Role type ('doctor' or 'patient')
            role_id: Role identifier

        Returns:
            Statistics dictionary
        """
        try:
            # Get total count and event type breakdown from new table
            result = self._get_database().table('medical_knowledge_base').select(
                'event_type, created_at'
            ).eq('role_type', role_type).eq('role_id', role_id).execute()

            if not result.data:
                return {
                    'total_entries': 0,
                    'event_types': {},
                    'latest_entry': None,
                    'earliest_entry': None
                }

            # Calculate statistics
            event_types = {}
            dates = []

            for row in result.data:
                event_type = row['event_type']
                event_types[event_type] = event_types.get(event_type, 0) + 1
                dates.append(row['created_at'])

            return {
                'total_entries': len(result.data),
                'event_types': event_types,
                'latest_entry': max(dates) if dates else None,
                'earliest_entry': min(dates) if dates else None,
                'role_type': role_type,
                'role_id': role_id
            }

        except Exception as e:
            logger.error(f"Failed to get medical knowledge stats: {e}")
            return {
                'total_entries': 0,
                'event_types': {},
                'latest_entry': None,
                'earliest_entry': None,
                'error': str(e)
            }

    async def get_patient_knowledge_stats(self, patient_id: str) -> Dict[str, Any]:
        """
        Legacy method for patient knowledge stats (backward compatibility)

        Args:
            patient_id: Patient identifier

        Returns:
            Statistics dictionary
        """
        # Use the new dual-role method
        return await self.get_medical_knowledge_stats('patient', patient_id)
    
    async def delete_patient_knowledge(self, patient_id: str, event_type: Optional[str] = None) -> bool:
        """
        Delete patient knowledge base entries
        
        Args:
            patient_id: Patient identifier
            event_type: Optional event type filter
            
        Returns:
            Success status
        """
        try:
            query_builder = self._get_database().table('patient_knowledge_base').delete().eq('patient_id', patient_id)
            
            if event_type:
                query_builder = query_builder.eq('event_type', event_type)
            
            result = query_builder.execute()
            
            logger.info(f"Deleted knowledge base entries for patient {patient_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete patient knowledge: {e}")
            return False
