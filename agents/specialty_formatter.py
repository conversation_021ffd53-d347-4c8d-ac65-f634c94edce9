"""
Specialty-Specific Formatting Agent
"""
import json
import logging
import re
from typing import Dict, Any, List

from langchain_openai import Chat<PERSON>penAI
from langchain_core.messages import HumanMessage, SystemMessage

from config.settings import settings, get_specialty_config
from models.schemas import SOAPNotes

logger = logging.getLogger(__name__)

class SpecialtyFormatterAgent:
    """Agent for applying specialty-specific formatting to SOAP notes"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.openai_model,
            temperature=0.1,  # Very low temperature for consistent formatting
            max_tokens=3000,
            openai_api_key=settings.openai_api_key
        )
    
    def requires_specialty_formatting(self, specialty: str, soap_notes: SOAPNotes = None) -> bool:
        """
        Determine if specialty-specific formatting is needed

        Args:
            specialty: Medical specialty
            soap_notes: Generated SOAP notes (optional)

        Returns:
            Boolean indicating if specialty formatting is required
        """
        # Always format for specific specialties (not general)
        if specialty.lower() == "general":
            return False

        specialty_config = get_specialty_config(specialty)

        # Check if specialty has specific requirements
        if not specialty_config.get('focus_areas') and not specialty_config.get('required_sections'):
            return False

        # If no soap_notes provided, just check if specialty is not general
        if soap_notes is None:
            return True

        # Check if current notes already contain specialty-specific content
        all_text = f"{soap_notes.subjective} {soap_notes.objective} {soap_notes.assessment} {soap_notes.plan}".lower()

        focus_areas = specialty_config.get('focus_areas', [])
        specialty_terms_found = sum(1 for area in focus_areas if area.lower() in all_text)

        # If less than 50% of focus areas are mentioned, needs specialty formatting
        if focus_areas and specialty_terms_found < len(focus_areas) * 0.5:
            return True

        return True  # Default to formatting for non-general specialties
    
    async def apply_specialty_formatting(
        self,
        soap_notes: SOAPNotes,
        specialty: str,
        original_transcription: str
    ) -> SOAPNotes:
        """
        Apply specialty-specific formatting to SOAP notes
        
        Args:
            soap_notes: Original SOAP notes
            specialty: Medical specialty
            original_transcription: Original transcription for context
            
        Returns:
            Specialty-formatted SOAP notes
        """
        if not self.requires_specialty_formatting(specialty, soap_notes):
            logger.info(f"No specialty formatting required for {specialty}")
            return soap_notes
            
        try:
            specialty_config = get_specialty_config(specialty)
            
            system_prompt = self._get_specialty_system_prompt(specialty, specialty_config)
            user_prompt = self._get_specialty_user_prompt(
                soap_notes, specialty, original_transcription, specialty_config
            )
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.llm.agenerate([messages])
            result_text = response.generations[0][0].text
            
            formatted_notes = self._parse_specialty_response(
                result_text, soap_notes, specialty
            )
            
            logger.info(f"Applied {specialty} specialty formatting")
            return formatted_notes
            
        except Exception as e:
            logger.error(f"Specialty formatting failed for {specialty}: {e}")
            # Return original notes if formatting fails
            return soap_notes
    
    def _get_specialty_system_prompt(self, specialty: str, specialty_config: dict) -> str:
        """Get system prompt for specialty formatting"""
        focus_areas = ", ".join(specialty_config.get('focus_areas', []))
        required_sections = ", ".join(specialty_config.get('required_sections', []))
        common_medications = ", ".join(specialty_config.get('common_medications', []))
        
        specialty_prompts = {
            'cardiology': """You are a Cardiology Specialist formatting SOAP notes.

CARDIOLOGY-SPECIFIC REQUIREMENTS:
- Include detailed cardiac history and risk factors
- Document ECG findings, cardiac enzymes, and imaging results
- Assess chest pain characteristics (quality, radiation, triggers)
- Include cardiac examination findings (heart sounds, murmurs, gallops)
- Document exercise tolerance and functional capacity
- Include relevant cardiac medications and dosing
- Assess cardiovascular risk stratification""",

            'dermatology': """You are a Dermatology Specialist formatting SOAP notes.

DERMATOLOGY-SPECIFIC REQUIREMENTS:
- Detailed lesion descriptions (size, color, texture, borders)
- Distribution patterns and morphology
- Dermatoscopy findings if applicable
- Skin type and photodamage assessment
- Previous skin cancer history
- Current topical and systemic treatments
- Biopsy recommendations and results""",

            'orthopedics': """You are an Orthopedic Specialist formatting SOAP notes.

ORTHOPEDIC-SPECIFIC REQUIREMENTS:
- Range of motion measurements (degrees)
- Strength testing (0-5 scale)
- Special orthopedic tests and results
- Imaging findings (X-ray, MRI, CT)
- Functional assessment and limitations
- Pain scale ratings and characteristics
- Rehabilitation and physical therapy plans""",

            'neurology': """You are a Neurology Specialist formatting SOAP notes.

NEUROLOGY-SPECIFIC REQUIREMENTS:
- Detailed neurological examination
- Mental status and cognitive assessment
- Cranial nerve examination (CN I-XII)
- Motor examination (strength, tone, reflexes)
- Sensory examination
- Coordination and gait assessment
- Neuroimaging findings
- Neuropsychological testing results""",

            'pediatrics': """You are a Pediatrics Specialist formatting SOAP notes.

PEDIATRIC-SPECIFIC REQUIREMENTS:
- Growth parameters (height, weight, head circumference percentiles)
- Developmental milestones assessment
- Age-appropriate examination techniques
- Immunization status and recommendations
- Parent/caregiver concerns and observations
- School performance and social development
- Pediatric-specific medication dosing (weight-based)"""
        }
        
        base_prompt = specialty_prompts.get(specialty.lower(), f"""You are a {specialty.title()} Specialist formatting SOAP notes.

SPECIALTY-SPECIFIC REQUIREMENTS:
- Focus on: {focus_areas}
- Include: {required_sections}
- Common medications: {common_medications}""")
        
        return f"""{base_prompt}

Your task is to enhance existing SOAP notes with specialty-specific details and formatting.

FORMATTING REQUIREMENTS:
- Maintain professional medical documentation standards
- Use appropriate medical terminology and abbreviations
- Include relevant specialty-specific assessments
- Ensure all sections are comprehensive and clinically relevant
- Add specialty-specific ICD-10 codes where appropriate
- Include relevant diagnostic criteria and scoring systems

Return structured JSON with enhanced SOAP sections:
{{
    "subjective": "enhanced subjective section",
    "objective": "enhanced objective section with specialty findings",
    "assessment": "enhanced assessment with specialty-specific diagnoses",
    "plan": "enhanced plan with specialty-specific treatments",
    "specialty_notes": "additional specialty-specific observations",
    "icd_codes": ["relevant specialty ICD-10 codes"]
}}

Return only valid JSON without additional text."""
    
    def _get_specialty_user_prompt(
        self,
        soap_notes: SOAPNotes,
        specialty: str,
        original_transcription: str,
        specialty_config: dict
    ) -> str:
        """Get user prompt for specialty formatting"""
        current_soap = f"""
CURRENT SOAP NOTES:
Subjective: {soap_notes.subjective}
Objective: {soap_notes.objective}
Assessment: {soap_notes.assessment}
Plan: {soap_notes.plan}
"""
        
        return f"""Please enhance these SOAP notes with {specialty} specialty-specific formatting:

{current_soap}

Original Transcription: {original_transcription}

Specialty Focus Areas: {', '.join(specialty_config.get('focus_areas', []))}
Required Sections: {', '.join(specialty_config.get('required_sections', []))}

Please enhance each section with appropriate {specialty} specialty details while maintaining the original clinical content."""
    
    def _parse_specialty_response(
        self,
        response_text: str,
        original_notes: SOAPNotes,
        specialty: str
    ) -> SOAPNotes:
        """Parse LLM response into enhanced SOAPNotes object"""
        try:
            result = json.loads(response_text)
            
            return SOAPNotes(
                subjective=result.get('subjective', original_notes.subjective),
                objective=result.get('objective', original_notes.objective),
                assessment=result.get('assessment', original_notes.assessment),
                plan=result.get('plan', original_notes.plan),
                session_id=original_notes.session_id,
                specialty=specialty,
                icd_codes=result.get('icd_codes', original_notes.icd_codes)
            )
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse specialty formatting response: {e}")
            logger.debug(f"Response text: {response_text}")
            
            # Return original notes if parsing fails
            return original_notes
    
    def get_specialty_requirements(self, specialty: str) -> Dict[str, Any]:
        """Get specialty-specific requirements for documentation"""
        specialty_config = get_specialty_config(specialty)

        return {
            'specialty': specialty,
            'focus_areas': specialty_config.get('focus_areas', []),
            'required_sections': specialty_config.get('required_sections', []),
            'common_medications': specialty_config.get('common_medications', []),
            'icd_codes_prefix': specialty_config.get('icd_codes_prefix', []),
            'formatting_required': specialty.lower() != "general"  # Simple check without soap_notes
        }
