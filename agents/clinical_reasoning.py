"""
Clinical Reasoning Agent
Enhances assessment section with diagnostic reasoning, confidence scores, and probabilities
"""
import json
import logging
from typing import Dict, List, Any, Optional

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

from config.settings import settings
from models.schemas import SOAPNotesStructured, AssessmentSection, PrimaryDiagnosis, DifferentialDiagnosis, ProblemListItem
from agents.specialty_detection import SpecialtyConfiguration
from utils.soap_parsing import SOAPParser

logger = logging.getLogger(__name__)

class ClinicalReasoningAgent:
    """Agent for enhancing assessment with clinical reasoning and diagnostic confidence"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.openai_model,
            temperature=0.2,  # Low temperature for consistent clinical reasoning
            max_tokens=2000,
            openai_api_key=settings.openai_api_key
        )
    
    async def enhance_assessment(
        self,
        soap_notes: SOAPNotesStructured,
        transcription: str,
        specialty_config: SpecialtyConfiguration
    ) -> SOAPNotesStructured:
        """
        Enhance assessment section with clinical reasoning and diagnostic confidence
        
        Args:
            soap_notes: Structured SOAP notes to enhance
            transcription: Original transcription for context
            specialty_config: Detected specialty configuration
            
        Returns:
            Enhanced SOAP notes with improved assessment section
        """
        system_prompt = self._get_reasoning_system_prompt(specialty_config)
        user_prompt = self._get_reasoning_user_prompt(soap_notes, transcription)
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        try:
            response = await self.llm.agenerate([messages])
            result_text = response.generations[0][0].text
            
            enhanced_assessment = self._parse_reasoning_response(result_text)
            
            # Create a new SOAP notes object with the enhanced assessment
            enhanced_soap = SOAPNotesStructured(
                subjective=soap_notes.subjective,
                objective=soap_notes.objective,
                assessment=enhanced_assessment,
                plan=soap_notes.plan,
                clinical_notes=soap_notes.clinical_notes
            )
            
            logger.info(f"Enhanced assessment with clinical reasoning")
            return enhanced_soap
            
        except Exception as e:
            logger.error(f"Clinical reasoning enhancement failed: {e}")
            # Return original notes if enhancement fails
            return soap_notes
    
    def _get_reasoning_system_prompt(self, specialty_config: SpecialtyConfiguration) -> str:
        """Get system prompt for clinical reasoning enhancement"""
        return f"""You are a Clinical Reasoning Expert specializing in {specialty_config.specialty.title()}. Your task is to enhance the assessment section of SOAP notes with detailed clinical reasoning, diagnostic confidence, and differential diagnoses.

SPECIALTY CONTEXT:
- Specialty: {specialty_config.specialty}
- Focus Areas: {', '.join(specialty_config.focus_areas)}
- Common Conditions: Related to ICD-10 prefixes {', '.join(specialty_config.icd_codes_prefix) if specialty_config.icd_codes_prefix else 'various'}
- Red Flags: {', '.join(specialty_config.red_flags)}

ENHANCEMENT REQUIREMENTS:
1. Analyze the primary diagnosis and add:
   - Clinical reasoning explaining diagnostic process
   - Confidence score (0.0-1.0) based on evidence strength
   - Severity assessment (mild/moderate/severe)
   - Appropriate ICD-10 code

2. Generate differential diagnoses with:
   - Probability scores (0.0-1.0)
   - Specific criteria for ruling out each differential
   - Appropriate ICD-10 codes

3. Create a comprehensive problem list with:
   - Status classification (active/resolved/chronic)
   - Priority level (high/medium/low)

4. Assess overall risk level (low/moderate/high)
5. Identify specific risk factors
6. Provide a concise prognosis

OUTPUT FORMAT:
Return ONLY valid JSON in this exact format:
{{
  "primary_diagnosis": {{
    "diagnosis": "string",
    "icd10_code": "string",
    "confidence": float,
    "severity": "mild/moderate/severe",
    "clinical_reasoning": "string"
  }},
  "differential_diagnoses": [
    {{
      "diagnosis": "string",
      "icd10_code": "string",
      "probability": float,
      "ruling_out_criteria": "string"
    }}
  ],
  "problem_list": [
    {{
      "problem": "string",
      "status": "active/resolved/chronic",
      "priority": "high/medium/low"
    }}
  ],
  "risk_level": "low/moderate/high",
  "risk_factors": ["factor1", "factor2"],
  "prognosis": "string"
}}

Be precise, clinically accurate, and evidence-based in your reasoning."""
    
    def _get_reasoning_user_prompt(self, soap_notes: SOAPNotesStructured, transcription: str) -> str:
        """Get user prompt for clinical reasoning enhancement"""
        # Extract key information from SOAP notes for context
        subjective_summary = f"Chief Complaint: {soap_notes.subjective.chief_complaint}\n" \
                            f"HPI: {soap_notes.subjective.history_present_illness}\n" \
                            f"PMH: {', '.join(soap_notes.subjective.past_medical_history)}\n" \
                            f"Medications: {', '.join(soap_notes.subjective.medications)}\n" \
                            f"Allergies: {', '.join(soap_notes.subjective.allergies)}"
        
        objective_summary = f"Vital Signs: {soap_notes.objective.vital_signs}\n" \
                           f"Physical Exam: {soap_notes.objective.physical_exam}\n" \
                           f"Diagnostic Results: {', '.join(soap_notes.objective.diagnostic_results)}"
        
        return f"""Please enhance the assessment section of these SOAP notes with detailed clinical reasoning:

SUBJECTIVE INFORMATION:
{subjective_summary}

OBJECTIVE INFORMATION:
{objective_summary}

CURRENT ASSESSMENT:
{soap_notes.assessment if hasattr(soap_notes, 'assessment') and soap_notes.assessment else "Assessment section needs to be created"}

ORIGINAL TRANSCRIPTION CONTEXT:
{transcription[:1000]}...

Please analyze this clinical information and return an enhanced assessment section with clinical reasoning, confidence scores, and differential diagnoses in the specified JSON format."""
    
    def _parse_reasoning_response(self, response_text: str) -> AssessmentSection:
        """Parse LLM response into AssessmentSection object"""
        try:
            result = json.loads(response_text)
            
            # Parse primary diagnosis
            primary = result.get('primary_diagnosis', {})
            primary_diagnosis = PrimaryDiagnosis(
                diagnosis=primary.get('diagnosis', 'Unspecified diagnosis'),
                icd10_code=primary.get('icd10_code', ''),
                confidence=float(primary.get('confidence', 0.5)),
                severity=primary.get('severity', 'moderate'),
                clinical_reasoning=primary.get('clinical_reasoning', '')
            )
            
            # Parse differential diagnoses
            differentials = []
            for diff in result.get('differential_diagnoses', []):
                differentials.append(DifferentialDiagnosis(
                    diagnosis=diff.get('diagnosis', ''),
                    icd10_code=diff.get('icd10_code', ''),
                    probability=float(diff.get('probability', 0.0)),
                    ruling_out_criteria=diff.get('ruling_out_criteria', '')
                ))
            
            # Parse problem list
            problems = []
            for prob in result.get('problem_list', []):
                problems.append(ProblemListItem(
                    problem=prob.get('problem', ''),
                    status=prob.get('status', 'active'),
                    priority=prob.get('priority', 'medium')
                ))
            
            # Create assessment section
            return AssessmentSection(
                primary_diagnosis=primary_diagnosis,
                differential_diagnoses=differentials,
                problem_list=problems,
                risk_level=result.get('risk_level', 'moderate'),
                risk_factors=result.get('risk_factors', []),
                prognosis=result.get('prognosis', '')
            )
            
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            logger.error(f"Failed to parse clinical reasoning response: {e}")
            logger.debug(f"Response text: {response_text}")
            
            # Return a basic assessment section as fallback
            return self._create_fallback_assessment()
    
    def _create_fallback_assessment(self) -> AssessmentSection:
        """Create a fallback assessment section when parsing fails"""
        return AssessmentSection(
            primary_diagnosis=PrimaryDiagnosis(
                diagnosis="Unable to determine diagnosis",
                icd10_code="",
                confidence=0.0,
                severity="moderate",
                clinical_reasoning="Clinical reasoning could not be generated due to processing error."
            ),
            differential_diagnoses=[],
            problem_list=[],
            risk_level="moderate",
            risk_factors=["Unable to determine risk factors"],
            prognosis="Prognosis could not be determined due to processing error."
        )
