"""
Embedding Service for RAG functionality
Handles text chunking and embedding generation using OpenAI
"""
import logging
import asyncio
import re
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone

import openai
import tiktoken
from openai import AsyncOpenA<PERSON>

from config.settings import settings

logger = logging.getLogger(__name__)

class EmbeddingService:
    """Service for generating embeddings and chunking text for RAG"""
    
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.openai_api_key)
        self.embedding_model = "text-embedding-3-small"  # OpenAI's latest embedding model
        self.embedding_dimension = 1536
        self.max_chunk_size = 1000  # Characters per chunk
        self.chunk_overlap = 200    # Overlap between chunks
        self.encoding = tiktoken.get_encoding("cl100k_base")
        
        logger.info(f"Embedding service initialized with model: {self.embedding_model}")
    
    async def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for a single text
        
        Args:
            text: Text to embed
            
        Returns:
            List of floats representing the embedding vector
        """
        try:
            # Clean and prepare text
            cleaned_text = self._clean_text(text)
            
            if not cleaned_text.strip():
                logger.warning("Empty text provided for embedding")
                return [0.0] * self.embedding_dimension
            
            # Generate embedding
            response = await self.client.embeddings.create(
                model=self.embedding_model,
                input=cleaned_text
            )
            
            embedding = response.data[0].embedding
            logger.debug(f"Generated embedding for text of length {len(text)}")
            
            return embedding
            
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            # Return zero vector as fallback
            return [0.0] * self.embedding_dimension
    
    async def generate_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts in batch
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        try:
            # Clean texts
            cleaned_texts = [self._clean_text(text) for text in texts]
            
            # Filter out empty texts
            valid_texts = [text for text in cleaned_texts if text.strip()]
            
            if not valid_texts:
                logger.warning("No valid texts provided for batch embedding")
                return [[0.0] * self.embedding_dimension] * len(texts)
            
            # Generate embeddings in batch
            response = await self.client.embeddings.create(
                model=self.embedding_model,
                input=valid_texts
            )
            
            embeddings = [item.embedding for item in response.data]
            logger.info(f"Generated {len(embeddings)} embeddings in batch")
            
            return embeddings
            
        except Exception as e:
            logger.error(f"Failed to generate batch embeddings: {e}")
            # Return zero vectors as fallback
            return [[0.0] * self.embedding_dimension] * len(texts)
    
    def chunk_text(self, text: str, metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Split text into chunks for embedding
        
        Args:
            text: Text to chunk
            metadata: Additional metadata to include with each chunk
            
        Returns:
            List of chunk dictionaries with text and metadata
        """
        try:
            # Clean text
            cleaned_text = self._clean_text(text)
            
            if len(cleaned_text) <= self.max_chunk_size:
                # Text is small enough, return as single chunk
                return [{
                    'text': cleaned_text,
                    'chunk_index': 0,
                    'total_chunks': 1,
                    'metadata': metadata or {}
                }]
            
            # Split into chunks
            chunks = []
            start = 0
            chunk_index = 0
            
            while start < len(cleaned_text):
                # Calculate end position
                end = start + self.max_chunk_size
                
                # If not the last chunk, try to break at word boundary
                if end < len(cleaned_text):
                    # Look for last space within overlap distance
                    last_space = cleaned_text.rfind(' ', end - self.chunk_overlap, end)
                    if last_space > start:
                        end = last_space
                
                # Extract chunk
                chunk_text = cleaned_text[start:end].strip()
                
                if chunk_text:
                    chunk_metadata = (metadata or {}).copy()
                    chunk_metadata.update({
                        'chunk_index': chunk_index,
                        'start_position': start,
                        'end_position': end
                    })
                    
                    chunks.append({
                        'text': chunk_text,
                        'chunk_index': chunk_index,
                        'metadata': chunk_metadata
                    })
                    
                    chunk_index += 1
                
                # Move start position (with overlap)
                start = max(start + 1, end - self.chunk_overlap)
            
            # Update total chunks count
            for chunk in chunks:
                chunk['total_chunks'] = len(chunks)
            
            logger.info(f"Split text into {len(chunks)} chunks")
            return chunks
            
        except Exception as e:
            logger.error(f"Failed to chunk text: {e}")
            return [{
                'text': text,
                'chunk_index': 0,
                'total_chunks': 1,
                'metadata': metadata or {}
            }]
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text for embedding"""
        if not text:
            return ""
        
        # Remove excessive whitespace
        cleaned = ' '.join(text.split())
        
        # Remove control characters
        cleaned = ''.join(char for char in cleaned if ord(char) >= 32 or char in '\n\t')
        
        return cleaned.strip()
    
    def calculate_token_count(self, text: str) -> int:
        """Calculate token count for text"""
        try:
            return len(self.encoding.encode(text))
        except Exception as e:
            logger.error(f"Failed to calculate token count: {e}")
            return len(text) // 4  # Rough estimate

    def _add_role_context_prefix(self, role_type: str, event_type: str, content: str) -> str:
        """Add role context prefix to content for better semantic understanding"""
        if role_type == 'doctor':
            prefix = f"[DOCTOR] {event_type.upper()}: "
        elif role_type == 'patient':
            prefix = f"[PATIENT] {event_type.upper()}: "
        else:
            prefix = f"[{role_type.upper()}] {event_type.upper()}: "

        return prefix + content

    def _enhance_appointment_content(self, original_content: str, appointment_details, role_type: str) -> str:
        """Enhance appointment content with structured information for better search"""
        enhanced_parts = [original_content]

        # Add structured information for better semantic search
        if appointment_details.patient_name:
            enhanced_parts.append(f"Patient: {appointment_details.patient_name}")

        if appointment_details.appointment_date:
            enhanced_parts.append(f"Date: {appointment_details.appointment_date}")

        if appointment_details.appointment_time:
            enhanced_parts.append(f"Time: {appointment_details.appointment_time}")

        if appointment_details.appointment_type:
            enhanced_parts.append(f"Type: {appointment_details.appointment_type}")

        if appointment_details.location:
            enhanced_parts.append(f"Location: {appointment_details.location}")

        if appointment_details.reason:
            enhanced_parts.append(f"Reason: {appointment_details.reason}")

        if appointment_details.special_instructions:
            enhanced_parts.append(f"Instructions: {appointment_details.special_instructions}")

        return ". ".join(enhanced_parts)

    def _parse_soap_content(self, content: str, event_type: str) -> dict:
        """Parse SOAP note content for structured data extraction"""
        soap_data = {}

        # Extract patient name
        patient_match = re.search(r'(?:patient|for patient)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)', content, re.IGNORECASE)
        if patient_match:
            soap_data['patient_name'] = patient_match.group(1).strip()

        # Extract chief complaint
        complaint_match = re.search(r'(?:chief complaint|complaint):\s*([^.]+)', content, re.IGNORECASE)
        if complaint_match:
            soap_data['chief_complaint'] = complaint_match.group(1).strip()

        # Extract diagnosis
        diagnosis_patterns = [
            r'(?:diagnosis|assessment):\s*([^.]+)',
            r'(?:primary diagnosis):\s*([^.]+)',
        ]
        for pattern in diagnosis_patterns:
            diagnosis_match = re.search(pattern, content, re.IGNORECASE)
            if diagnosis_match:
                soap_data['diagnosis'] = diagnosis_match.group(1).strip()
                break

        # Extract medications
        med_match = re.search(r'(?:medications?):\s*([^.]+)', content, re.IGNORECASE)
        if med_match:
            meds = [med.strip() for med in med_match.group(1).split(',')]
            soap_data['medications'] = meds

        # Extract vital signs
        vitals_patterns = [
            (r'BP\s*(\d+/\d+)', 'blood_pressure'),
            (r'HR\s*(\d+)', 'heart_rate'),
            (r'RR\s*(\d+)', 'respiratory_rate'),
            (r'O2\s*sat\s*(\d+%)', 'oxygen_saturation'),
        ]
        vitals = {}
        for pattern, key in vitals_patterns:
            vital_match = re.search(pattern, content, re.IGNORECASE)
            if vital_match:
                vitals[key] = vital_match.group(1)
        if vitals:
            soap_data['vital_signs'] = vitals

        # Extract quality score
        quality_match = re.search(r'quality score:\s*(\d+)%?', content, re.IGNORECASE)
        if quality_match:
            soap_data['quality_score'] = int(quality_match.group(1))

        # Extract safety status
        if 'safety status: safe' in content.lower():
            soap_data['safety_status'] = True
        elif 'safety status:' in content.lower():
            soap_data['safety_status'] = False

        return soap_data

    def _enhance_soap_content(self, original_content: str, soap_data: dict, role_type: str) -> str:
        """Enhance SOAP content with structured information for better search"""
        enhanced_parts = [original_content]

        # Add structured information for better semantic search
        if soap_data.get('patient_name'):
            enhanced_parts.append(f"Patient: {soap_data['patient_name']}")

        if soap_data.get('chief_complaint'):
            enhanced_parts.append(f"Chief Complaint: {soap_data['chief_complaint']}")

        if soap_data.get('diagnosis'):
            enhanced_parts.append(f"Diagnosis: {soap_data['diagnosis']}")

        if soap_data.get('medications'):
            enhanced_parts.append(f"Medications: {', '.join(soap_data['medications'])}")

        if soap_data.get('vital_signs'):
            vitals = soap_data['vital_signs']
            vital_parts = []
            for key, value in vitals.items():
                vital_parts.append(f"{key.replace('_', ' ').title()}: {value}")
            enhanced_parts.append(f"Vital Signs: {', '.join(vital_parts)}")

        if soap_data.get('quality_score'):
            enhanced_parts.append(f"Quality Score: {soap_data['quality_score']}%")

        if soap_data.get('safety_status') is not None:
            status = "Safe" if soap_data['safety_status'] else "Requires Attention"
            enhanced_parts.append(f"Safety Status: {status}")

        return ". ".join(enhanced_parts)

    async def embed_medical_data(
        self,
        role_type: str,
        role_id: str,
        event_type: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Process medical data for embedding storage (supports both doctors and patients)

        Args:
            role_type: Role type ('doctor' or 'patient')
            role_id: Role identifier (doctor_id or patient_id)
            event_type: Type of event (varies by role)
            content: Text content to embed
            metadata: Additional metadata

        Returns:
            List of processed chunks ready for database storage
        """
        try:
            # Parse structured data for specific event types
            structured_data = None
            if event_type == 'appointment_scheduled':
                from models.schemas import AppointmentDetails
                structured_data = AppointmentDetails.parse_from_text(content, role_type)

                # Enhance content with structured information
                enhanced_content = self._enhance_appointment_content(content, structured_data, role_type)
                role_prefixed_content = self._add_role_context_prefix(role_type, event_type, enhanced_content)
            elif event_type.startswith('soap_note_'):
                # Enhanced SOAP note processing
                soap_data = self._parse_soap_content(content, event_type)
                if soap_data:
                    structured_data = soap_data
                    enhanced_content = self._enhance_soap_content(content, soap_data, role_type)
                    role_prefixed_content = self._add_role_context_prefix(role_type, event_type, enhanced_content)
                else:
                    role_prefixed_content = self._add_role_context_prefix(role_type, event_type, content)
            else:
                # Add role context prefix to content for better semantic understanding
                role_prefixed_content = self._add_role_context_prefix(role_type, event_type, content)

            # Add role and event info to metadata
            full_metadata = {
                'role_type': role_type,
                'role_id': role_id,
                'event_type': event_type,
                'created_at': datetime.now(timezone.utc).isoformat(),
                'processing_version': '2.1',  # Updated version for structured appointments
                **(metadata or {})
            }

            # Add structured data to metadata
            if structured_data:
                if hasattr(structured_data, 'to_dict'):
                    # For appointment data with to_dict method
                    full_metadata['appointment_details'] = structured_data.to_dict()
                else:
                    # For SOAP data that's already a dictionary
                    full_metadata['soap_details'] = structured_data

            # Chunk the text
            chunks = self.chunk_text(role_prefixed_content, full_metadata)

            # Generate embeddings for all chunks
            chunk_texts = [chunk['text'] for chunk in chunks]
            embeddings = await self.generate_embeddings_batch(chunk_texts)

            # Combine chunks with embeddings
            processed_chunks = []
            for chunk, embedding in zip(chunks, embeddings):
                processed_chunks.append({
                    'role_type': role_type,
                    'role_id': role_id,
                    'event_type': event_type,
                    'content': content,  # Store original content without prefix
                    'content_chunk': chunk['text'],
                    'embedding': embedding,
                    'metadata': chunk['metadata']
                })

            logger.info(f"Processed {len(processed_chunks)} chunks for {role_type} {role_id}")
            return processed_chunks

        except Exception as e:
            logger.error(f"Failed to embed medical data: {e}")
            return []

    async def embed_patient_data(
        self, 
        patient_id: str, 
        event_type: str, 
        content: str, 
        metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Legacy method for patient data embedding (backward compatibility)

        Args:
            patient_id: Patient identifier
            event_type: Type of event (soap, conversation, medication, etc.)
            content: Text content to embed
            metadata: Additional metadata

        Returns:
            List of processed chunks ready for database storage
        """
        # Use the new dual-role method with patient role
        chunks = await self.embed_medical_data('patient', patient_id, event_type, content, metadata)

        # Convert to legacy format for backward compatibility
        legacy_chunks = []
        for chunk in chunks:
            legacy_chunk = chunk.copy()
            legacy_chunk['patient_id'] = chunk['role_id']
            # Remove new fields for backward compatibility
            legacy_chunk.pop('role_type', None)
            legacy_chunk.pop('role_id', None)
            legacy_chunks.append(legacy_chunk)

        return legacy_chunks
