"""
Medical-Grade Error Handling Service
Comprehensive error handling with medical safety focus
"""
import logging
import asyncio
import traceback
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from enum import Enum

from models.schemas import SOAPNotes, TranscriptionResult, ValidationResult

logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    """Error severity levels for medical context"""
    CRITICAL = "critical"  # Patient safety at risk
    HIGH = "high"         # Clinical accuracy compromised
    MEDIUM = "medium"     # Quality degraded but safe
    LOW = "low"          # Minor issues

@dataclass
class MedicalError:
    """Medical error with context"""
    error_id: str
    severity: ErrorSeverity
    component: str  # Which agent/service failed
    error_type: str
    message: str
    patient_id: Optional[str]
    session_id: Optional[str]
    timestamp: datetime
    stack_trace: Optional[str]
    recovery_action: Optional[str]
    requires_manual_review: bool

@dataclass
class FallbackResponse:
    """Fallback response for critical failures"""
    success: bool
    fallback_type: str
    message: str
    data: Optional[Dict[str, Any]]
    requires_human_review: bool

class MedicalErrorHandler:
    """Medical-grade error handling with safety-first approach"""
    
    def __init__(self):
        self.error_log: List[MedicalError] = []
        self.alert_callbacks: List[Callable] = []
        self.fallback_templates = self._load_fallback_templates()
    
    async def handle_transcription_error(
        self, 
        error: Exception, 
        patient_id: str, 
        audio_file: str
    ) -> FallbackResponse:
        """Handle transcription failures with medical safety focus"""
        
        medical_error = MedicalError(
            error_id=f"TRANS_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            severity=ErrorSeverity.HIGH,
            component="transcription_agent",
            error_type=type(error).__name__,
            message=str(error),
            patient_id=patient_id,
            session_id=None,
            timestamp=datetime.now(timezone.utc),
            stack_trace=traceback.format_exc(),
            recovery_action="manual_transcription_required",
            requires_manual_review=True
        )
        
        await self._log_and_alert(medical_error)
        
        # Critical: Don't proceed without transcription
        return FallbackResponse(
            success=False,
            fallback_type="transcription_failure",
            message="Audio transcription failed. Manual transcription required for patient safety.",
            data={
                "error_id": medical_error.error_id,
                "recommended_action": "Have medical staff manually transcribe audio",
                "patient_id": patient_id,
                "audio_file": audio_file
            },
            requires_human_review=True
        )
    
    async def handle_validation_error(
        self, 
        error: Exception, 
        transcription: TranscriptionResult,
        patient_id: str
    ) -> FallbackResponse:
        """Handle medical validation failures"""
        
        medical_error = MedicalError(
            error_id=f"VALID_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            severity=ErrorSeverity.MEDIUM,
            component="validation_agent",
            error_type=type(error).__name__,
            message=str(error),
            patient_id=patient_id,
            session_id=None,
            timestamp=datetime.now(timezone.utc),
            stack_trace=traceback.format_exc(),
            recovery_action="use_original_transcription_with_warning",
            requires_manual_review=True
        )
        
        await self._log_and_alert(medical_error)
        
        # Use original transcription but flag for review
        fallback_validation = ValidationResult(
            validated_text=transcription.text,
            corrections=[],
            flags=[{
                "type": "system_error",
                "message": f"Validation failed: {str(error)}",
                "severity": "high"
            }],
            confidence=0.5  # Low confidence due to validation failure
        )
        
        return FallbackResponse(
            success=True,
            fallback_type="validation_bypass",
            message="Medical validation failed. Using original transcription with mandatory review.",
            data={
                "validation_result": fallback_validation,
                "error_id": medical_error.error_id,
                "warning": "Manual medical terminology review required"
            },
            requires_human_review=True
        )
    
    async def handle_soap_generation_error(
        self, 
        error: Exception, 
        validated_text: str,
        patient_id: str,
        specialty: str
    ) -> FallbackResponse:
        """Handle SOAP notes generation failures"""
        
        medical_error = MedicalError(
            error_id=f"SOAP_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            severity=ErrorSeverity.CRITICAL,
            component="soap_generation_agent",
            error_type=type(error).__name__,
            message=str(error),
            patient_id=patient_id,
            session_id=None,
            timestamp=datetime.now(timezone.utc),
            stack_trace=traceback.format_exc(),
            recovery_action="use_emergency_template",
            requires_manual_review=True
        )
        
        await self._log_and_alert(medical_error)
        
        # Use emergency fallback template
        emergency_soap = self._generate_emergency_soap_notes(
            validated_text, patient_id, specialty, medical_error.error_id
        )
        
        return FallbackResponse(
            success=True,
            fallback_type="emergency_soap_template",
            message="SOAP generation failed. Emergency template used - REQUIRES IMMEDIATE PHYSICIAN REVIEW.",
            data={
                "soap_notes": emergency_soap,
                "error_id": medical_error.error_id,
                "critical_warning": "This document was generated using emergency fallback. Complete physician review required before any clinical use."
            },
            requires_human_review=True
        )
    
    async def handle_qa_error(
        self, 
        error: Exception, 
        soap_notes: SOAPNotes,
        patient_id: str
    ) -> FallbackResponse:
        """Handle quality assurance failures"""
        
        medical_error = MedicalError(
            error_id=f"QA_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            severity=ErrorSeverity.HIGH,
            component="quality_assurance_agent",
            error_type=type(error).__name__,
            message=str(error),
            patient_id=patient_id,
            session_id=soap_notes.session_id,
            timestamp=datetime.now(timezone.utc),
            stack_trace=traceback.format_exc(),
            recovery_action="mandatory_manual_qa",
            requires_manual_review=True
        )
        
        await self._log_and_alert(medical_error)
        
        # Conservative approach: Fail QA and require manual review
        from models.schemas import QualityAssessment
        
        failed_qa = QualityAssessment(
            quality_score=0,
            errors=[f"QA system failure: {str(error)}"],
            warnings=["Automated quality assurance failed"],
            recommendations=["MANDATORY: Complete manual physician review required"],
            critical_flags=[{
                "type": "system_failure",
                "message": "Quality assurance system failed",
                "severity": "critical"
            }],
            approved=False
        )
        
        return FallbackResponse(
            success=True,
            fallback_type="qa_failure_conservative",
            message="Quality assurance failed. Document marked for mandatory manual review.",
            data={
                "qa_results": failed_qa,
                "error_id": medical_error.error_id,
                "mandatory_action": "Complete physician review required before any clinical use"
            },
            requires_human_review=True
        )
    
    async def handle_critical_system_error(
        self, 
        error: Exception, 
        context: Dict[str, Any]
    ) -> FallbackResponse:
        """Handle critical system-wide errors"""
        
        medical_error = MedicalError(
            error_id=f"CRIT_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            severity=ErrorSeverity.CRITICAL,
            component="system",
            error_type=type(error).__name__,
            message=str(error),
            patient_id=context.get('patient_id'),
            session_id=context.get('session_id'),
            timestamp=datetime.now(timezone.utc),
            stack_trace=traceback.format_exc(),
            recovery_action="system_shutdown_required",
            requires_manual_review=True
        )
        
        await self._log_and_alert(medical_error)
        
        # System-wide failure - stop all processing
        return FallbackResponse(
            success=False,
            fallback_type="critical_system_failure",
            message="CRITICAL SYSTEM ERROR: All automated processing stopped. Immediate technical and medical review required.",
            data={
                "error_id": medical_error.error_id,
                "system_status": "OFFLINE",
                "immediate_action": "Contact system administrator and medical director immediately",
                "patient_safety_notice": "Revert to manual documentation processes until system is restored"
            },
            requires_human_review=True
        )
    
    def _generate_emergency_soap_notes(
        self, 
        validated_text: str, 
        patient_id: str, 
        specialty: str,
        error_id: str
    ) -> SOAPNotes:
        """Generate emergency SOAP notes template"""
        
        emergency_template = self.fallback_templates.get(specialty, self.fallback_templates['general'])
        
        return SOAPNotes(
            subjective=f"[EMERGENCY TEMPLATE - ERROR {error_id}]\n\nPatient transcript: {validated_text}\n\n[WARNING: This section requires complete physician review and rewriting]",
            objective=emergency_template['objective'],
            assessment=emergency_template['assessment'],
            plan=emergency_template['plan'],
            session_id=f"emergency_{error_id}",
            specialty=specialty,
            icd_codes=[]
        )
    
    def _load_fallback_templates(self) -> Dict[str, Dict[str, str]]:
        """Load emergency fallback templates for different specialties"""
        return {
            'general': {
                'objective': "[EMERGENCY TEMPLATE] Physical examination and vital signs to be documented by attending physician.",
                'assessment': "[EMERGENCY TEMPLATE] Clinical assessment to be completed by attending physician based on patient presentation.",
                'plan': "[EMERGENCY TEMPLATE] Treatment plan to be developed by attending physician after complete evaluation."
            },
            'internal_medicine': {
                'objective': "[EMERGENCY TEMPLATE] Complete physical examination, vital signs, and relevant laboratory/imaging results to be documented.",
                'assessment': "[EMERGENCY TEMPLATE] Differential diagnosis and clinical reasoning to be provided by attending internist.",
                'plan': "[EMERGENCY TEMPLATE] Comprehensive treatment plan including medications, follow-up, and monitoring to be established."
            },
            'cardiology': {
                'objective': "[EMERGENCY TEMPLATE] Cardiovascular examination, ECG, and cardiac-specific assessments to be documented.",
                'assessment': "[EMERGENCY TEMPLATE] Cardiac risk stratification and diagnosis to be completed by cardiologist.",
                'plan': "[EMERGENCY TEMPLATE] Cardiac management plan including medications, procedures, and monitoring to be established."
            }
        }
    
    async def _log_and_alert(self, medical_error: MedicalError):
        """Log error and send alerts to medical staff"""
        
        # Add to error log
        self.error_log.append(medical_error)
        
        # Log with appropriate level
        if medical_error.severity == ErrorSeverity.CRITICAL:
            logger.critical(f"CRITICAL MEDICAL ERROR: {medical_error.message} [ID: {medical_error.error_id}]")
        elif medical_error.severity == ErrorSeverity.HIGH:
            logger.error(f"HIGH PRIORITY MEDICAL ERROR: {medical_error.message} [ID: {medical_error.error_id}]")
        else:
            logger.warning(f"MEDICAL ERROR: {medical_error.message} [ID: {medical_error.error_id}]")
        
        # Send alerts for critical and high severity errors
        if medical_error.severity in [ErrorSeverity.CRITICAL, ErrorSeverity.HIGH]:
            await self._send_medical_staff_alert(medical_error)
    
    async def _send_medical_staff_alert(self, medical_error: MedicalError):
        """Send alert to medical staff (placeholder for real implementation)"""
        
        alert_message = f"""
        MEDICAL SYSTEM ALERT
        
        Error ID: {medical_error.error_id}
        Severity: {medical_error.severity.value.upper()}
        Component: {medical_error.component}
        Patient ID: {medical_error.patient_id}
        Time: {medical_error.timestamp}
        
        Message: {medical_error.message}
        
        Required Action: {medical_error.recovery_action}
        Manual Review Required: {medical_error.requires_manual_review}
        """
        
        # In production, this would send to:
        # - Medical director
        # - IT support
        # - Attending physicians
        # - Quality assurance team
        
        logger.critical(f"MEDICAL STAFF ALERT SENT: {alert_message}")
        
        # Execute any registered alert callbacks
        for callback in self.alert_callbacks:
            try:
                await callback(medical_error)
            except Exception as e:
                logger.error(f"Alert callback failed: {e}")
    
    def add_alert_callback(self, callback: Callable):
        """Add callback for medical alerts"""
        self.alert_callbacks.append(callback)
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of recent errors"""
        recent_errors = [e for e in self.error_log if 
                        (datetime.now(timezone.utc) - e.timestamp).total_seconds() < 3600]  # Last hour
        
        return {
            'total_errors_last_hour': len(recent_errors),
            'critical_errors': len([e for e in recent_errors if e.severity == ErrorSeverity.CRITICAL]),
            'high_priority_errors': len([e for e in recent_errors if e.severity == ErrorSeverity.HIGH]),
            'components_affected': list(set([e.component for e in recent_errors])),
            'patients_affected': list(set([e.patient_id for e in recent_errors if e.patient_id]))
        }
