"""
RAG (Retrieval-Augmented Generation) Service
Orchestrates the complete RAG pipeline for patient-specific queries
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

from config.settings import settings
from services.vector_search_service import VectorSearchService
from services.embedding_service import EmbeddingService

logger = logging.getLogger(__name__)

class RAGService:
    """Main RAG service for patient-specific knowledge retrieval and generation"""
    
    def __init__(self):
        self.vector_search = VectorSearchService()
        self.embedding_service = EmbeddingService()
        self.llm = ChatOpenAI(
            model=settings.openai_model,
            temperature=0.3,
            max_tokens=2000,
            openai_api_key=settings.openai_api_key
        )
        
        logger.info("RAG service initialized")
    
    async def store_medical_data(
        self,
        role_type: str,
        role_id: str,
        event_type: str,
        data: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Store medical data in the knowledge base with embeddings (supports both doctors and patients)

        Args:
            role_type: Role type ('doctor' or 'patient')
            role_id: Role identifier (doctor_id or patient_id)
            event_type: Type of event (varies by role)
            data: Text data to store
            metadata: Additional metadata

        Returns:
            Storage result with status and details
        """
        try:
            start_time = datetime.now()

            # Validate inputs
            if not role_type or not role_id or not event_type or not data:
                return {
                    'success': False,
                    'error': 'Missing required parameters: role_type, role_id, event_type, or data',
                    'role_type': role_type,
                    'role_id': role_id,
                    'event_type': event_type
                }

            # Validate role type
            if role_type not in ['doctor', 'patient']:
                return {
                    'success': False,
                    'error': 'Invalid role_type. Must be "doctor" or "patient"',
                    'role_type': role_type,
                    'role_id': role_id,
                    'event_type': event_type
                }

            # Add processing metadata
            processing_metadata = {
                'stored_at': datetime.now(timezone.utc).isoformat(),
                'data_length': len(data),
                'processing_version': '2.0',  # Updated for dual-role
                'role_type': role_type,
                **(metadata or {})
            }

            # Store in vector database
            success = await self.vector_search.store_medical_embeddings(
                role_type, role_id, event_type, data, processing_metadata
            )

            processing_time = (datetime.now() - start_time).total_seconds()

            if success:
                # Get updated stats
                stats = await self.vector_search.get_medical_knowledge_stats(role_type, role_id)

                return {
                    'success': True,
                    'message': f'Successfully stored {event_type} data for {role_type} {role_id}',
                    'role_type': role_type,
                    'role_id': role_id,
                    'event_type': event_type,
                    'data_length': len(data),
                    'processing_time_seconds': processing_time,
                    'knowledge_base_stats': stats,
                    'stored_at': datetime.now(timezone.utc).isoformat()
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to store data in knowledge base',
                    'role_type': role_type,
                    'role_id': role_id,
                    'event_type': event_type,
                    'processing_time_seconds': processing_time
                }

        except Exception as e:
            logger.error(f"Failed to store medical data: {e}")
            return {
                'success': False,
                'error': f'Storage failed: {str(e)}',
                'role_type': role_type,
                'role_id': role_id,
                'event_type': event_type
            }

    async def store_patient_data(
        self,
        patient_id: str,
        event_type: str,
        data: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Legacy method for storing patient data (backward compatibility)

        Args:
            patient_id: Patient identifier
            event_type: Type of event (soap, conversation, medication, appointment, etc.)
            data: Text data to store
            metadata: Additional metadata

        Returns:
            Storage result with status and details
        """
        # Use the new dual-role method
        result = await self.store_medical_data('patient', patient_id, event_type, data, metadata)

        # Convert response to legacy format for backward compatibility
        if result['success']:
            legacy_result = result.copy()
            legacy_result['patient_id'] = result['role_id']
            # Remove new fields for backward compatibility
            legacy_result.pop('role_type', None)
            legacy_result.pop('role_id', None)
            return legacy_result
        else:
            return result

    async def query_medical_knowledge(
        self,
        role_type: str,
        role_id: str,
        query: str,
        similarity_threshold: float = 0.7,
        max_results: int = 5,
        event_types: Optional[List[str]] = None,
        include_context: bool = True,
        cross_role_search: bool = False
    ) -> Dict[str, Any]:
        """
        Query medical knowledge base and generate response (supports both doctors and patients)

        Args:
            role_type: Role type ('doctor' or 'patient')
            role_id: Role identifier (doctor_id or patient_id)
            query: User query
            similarity_threshold: Minimum similarity for relevant documents
            max_results: Maximum number of documents to retrieve
            event_types: Filter by specific event types
            include_context: Whether to include retrieved context in response
            cross_role_search: Whether to search across roles (for doctors)

        Returns:
            Query result with generated response and context
        """
        try:
            start_time = datetime.now()

            # Validate inputs
            if not role_type or not role_id or not query:
                return {
                    'success': False,
                    'error': 'Missing required parameters: role_type, role_id, or query',
                    'role_type': role_type,
                    'role_id': role_id,
                    'query': query
                }

            # Search relevant documents
            relevant_docs = await self.vector_search.search_medical_knowledge(
                role_type, role_id, query, similarity_threshold, max_results,
                event_types, cross_role_search
            )

            processing_time = (datetime.now() - start_time).total_seconds()

            if not relevant_docs:
                return {
                    'success': True,
                    'message': f'No relevant information found for {role_type} {role_id}',
                    'role_type': role_type,
                    'role_id': role_id,
                    'query': query,
                    'response': 'I don\'t have any relevant information to answer your question.',
                    'structured_response': self._create_structured_response('text', 'No relevant information found', {}),
                    'relevant_documents_count': 0,
                    'relevant_documents': [],
                    'context_used': False,
                    'similarity_threshold': similarity_threshold,
                    'max_results': max_results,
                    'processing_time_seconds': processing_time,
                    'generated_at': datetime.now(timezone.utc).isoformat()
                }

            # Generate contextual response
            context = self._format_context_for_llm(relevant_docs)
            response_text = await self._generate_contextual_response(
                query, context, role_type, role_id
            )

            # Create structured response based on query type and role
            structured_response = self._create_structured_response_from_query(
                query, relevant_docs, role_type, response_text
            )

            return {
                'success': True,
                'message': f'Successfully generated response for {role_type} {role_id}',
                'role_type': role_type,
                'role_id': role_id,
                'query': query,
                'response': response_text,
                'structured_response': structured_response,
                'relevant_documents_count': len(relevant_docs),
                'relevant_documents': relevant_docs if include_context else [],
                'context_used': True,
                'similarity_threshold': similarity_threshold,
                'max_results': max_results,
                'processing_time_seconds': processing_time,
                'generated_at': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to query medical knowledge: {e}")
            return {
                'success': False,
                'error': f'Query failed: {str(e)}',
                'role_type': role_type,
                'role_id': role_id,
                'query': query
            }

    async def query_patient_knowledge(
        self, 
        patient_id: str, 
        query: str,
        similarity_threshold: float = 0.7,
        max_results: int = 5,
        event_types: Optional[List[str]] = None,
        include_context: bool = True
    ) -> Dict[str, Any]:
        """
        Legacy method for patient knowledge query (backward compatibility)

        Args:
            patient_id: Patient identifier
            query: User query
            similarity_threshold: Minimum similarity for relevant documents
            max_results: Maximum number of documents to retrieve
            event_types: Filter by specific event types
            include_context: Whether to include retrieved context in response

        Returns:
            Query result with generated response and context
        """
        # Use the new dual-role method
        result = await self.query_medical_knowledge(
            'patient', patient_id, query, similarity_threshold, max_results,
            event_types, include_context, cross_role_search=False
        )

        # Convert response to legacy format for backward compatibility
        if result['success']:
            legacy_result = result.copy()
            legacy_result['patient_id'] = result['role_id']
            # Remove new fields for backward compatibility
            legacy_result.pop('role_type', None)
            legacy_result.pop('role_id', None)
            legacy_result.pop('structured_response', None)  # Remove structured response for legacy
            return legacy_result
        else:
            return result

    def _format_context_for_llm(self, relevant_docs: List[Dict]) -> str:
        """Format relevant documents into context for LLM."""
        if not relevant_docs:
            return ""

        context_parts = []
        for i, doc in enumerate(relevant_docs, 1):
            content = doc.get('content_chunk', doc.get('content', ''))
            event_type = doc.get('event_type', 'unknown')
            created_at = doc.get('created_at', '')

            context_part = f"Document {i} ({event_type}):\n{content}"
            if created_at:
                context_part += f"\n(Created: {created_at})"

            context_parts.append(context_part)

        return "\n\n".join(context_parts)
    
    async def _generate_contextual_response(
        self,
        query: str,
        context: str,
        role_type: str,
        role_id: str
    ) -> str:
        """
        Generate response using retrieved context and LLM

        Args:
            query: User query
            context: Formatted context from relevant documents
            role_type: Type of role (doctor/patient)
            role_id: Role identifier

        Returns:
            Generated response text
        """
        try:
            # Use the provided context directly
            if not context.strip():
                return "I don't have any relevant information to answer your question."

            # Create system prompt based on role type
            if role_type == "doctor":
                system_prompt = f"""You are a medical AI assistant helping a doctor access their professional information.

Your role is to:
1. Answer questions about the doctor's schedule, patients, and professional activities
2. Provide accurate, helpful responses based on the retrieved information
3. Clearly indicate when information is not available in the provided context
4. Use appropriate professional language
5. Never make up information not present in the context

Doctor ID: {role_id}

IMPORTANT GUIDELINES:
- Only use information from the provided context
- If the context doesn't contain relevant information, clearly state this
- Maintain professional accuracy
- Be concise but thorough in your responses"""
            else:
                system_prompt = f"""You are a medical AI assistant with access to patient-specific medical records and history.

Your role is to:
1. Answer questions about the patient's medical history using only the provided context
2. Provide accurate, helpful responses based on the retrieved medical information
3. Clearly indicate when information is not available in the provided context
4. Maintain medical accuracy and appropriate clinical language
5. Never make up information not present in the context

Patient ID: {role_id}

IMPORTANT GUIDELINES:
- Only use information from the provided context
- If the context doesn't contain relevant information, clearly state this
- Maintain patient confidentiality and medical ethics
- Use appropriate medical terminology
- Be concise but thorough in your responses"""

            # Create user prompt
            user_prompt = f"""Based on the following information for {role_type} {role_id}, please answer this question:

QUESTION: {query}

RELEVANT CONTEXT:
{context}

Please provide a comprehensive answer based only on the information provided above. If the context doesn't contain sufficient information to answer the question, please state this clearly."""

            # Generate response
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.llm.agenerate([messages])
            generated_text = response.generations[0][0].text

            logger.info(f"Generated contextual response for {role_type} {role_id}")
            return generated_text

        except Exception as e:
            logger.error(f"Failed to generate contextual response: {e}")
            return f"I apologize, but I encountered an error while processing your query. Please try again or contact support if the issue persists."
    
    async def get_patient_knowledge_summary(self, patient_id: str) -> Dict[str, Any]:
        """
        Get summary of patient's knowledge base
        
        Args:
            patient_id: Patient identifier
            
        Returns:
            Knowledge base summary
        """
        try:
            stats = await self.vector_search.get_patient_knowledge_stats(patient_id)
            
            return {
                'success': True,
                'patient_id': patient_id,
                'knowledge_base_summary': stats,
                'generated_at': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get patient knowledge summary: {e}")
            return {
                'success': False,
                'error': f'Failed to get summary: {str(e)}',
                'patient_id': patient_id
            }

    async def get_medical_knowledge_summary(
        self,
        role_type: str,
        role_id: str
    ) -> Dict[str, Any]:
        """
        Get summary of medical knowledge base for a specific role

        Args:
            role_type: Role type ('doctor' or 'patient')
            role_id: Role identifier

        Returns:
            Knowledge base summary
        """
        try:
            stats = await self.vector_search.get_medical_knowledge_stats(role_type, role_id)

            return {
                'success': True,
                'role_type': role_type,
                'role_id': role_id,
                'knowledge_base_summary': stats,
                'generated_at': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get medical knowledge summary: {e}")
            return {
                'success': False,
                'error': f'Failed to get summary: {str(e)}',
                'role_type': role_type,
                'role_id': role_id
            }

    def _create_structured_response(
        self,
        response_type: str,
        summary: str,
        data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create a structured response object"""
        return {
            'type': response_type,
            'summary': summary,
            'data': data,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

    def _create_structured_response_from_query(
        self,
        query: str,
        relevant_docs: List[Dict[str, Any]],
        role_type: str,
        response_text: str
    ) -> Dict[str, Any]:
        """Create structured response based on query analysis"""
        query_lower = query.lower()

        # Analyze query to determine response type
        if any(word in query_lower for word in ['appointment', 'schedule', 'book', 'available']):
            if role_type == 'doctor':
                return self._create_structured_response(
                    'schedule_overview',
                    'Schedule and appointment information',
                    self._extract_schedule_data(relevant_docs)
                )
            else:
                return self._create_structured_response(
                    'appointment_status',
                    'Appointment status and requests',
                    self._extract_appointment_data(relevant_docs)
                )

        elif any(word in query_lower for word in ['medication', 'drug', 'prescription', 'pill']):
            return self._create_structured_response(
                'medication',
                'Medication information and history',
                self._extract_medication_data(relevant_docs)
            )

        elif any(word in query_lower for word in ['patient', 'list', 'who']):
            if role_type == 'doctor':
                return self._create_structured_response(
                    'patient_list',
                    'Patient information',
                    self._extract_patient_data(relevant_docs)
                )

        elif any(word in query_lower for word in ['symptom', 'pain', 'feel']):
            return self._create_structured_response(
                'symptom_analysis',
                'Symptom and health status information',
                self._extract_symptom_data(relevant_docs)
            )

        elif any(word in query_lower for word in ['soap', 'note', 'record']):
            return self._create_structured_response(
                'soap_note',
                'Clinical notes and records',
                self._extract_soap_data(relevant_docs)
            )

        # Default to text response
        return self._create_structured_response(
            'text',
            'General medical information',
            {'response': response_text, 'source_count': len(relevant_docs)}
        )

    def _extract_schedule_data(self, docs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract schedule-related data from documents"""
        schedule_data = {'available_slots': [], 'booked_appointments': []}

        for doc in docs:
            if doc.get('event_type') == 'schedule_slots':
                # Parse schedule data from content
                schedule_data['available_slots'].append({
                    'date': doc.get('metadata', {}).get('date'),
                    'content': doc.get('content_chunk', '')
                })
            elif doc.get('event_type') == 'appointment_booked':
                schedule_data['booked_appointments'].append({
                    'date': doc.get('metadata', {}).get('date'),
                    'content': doc.get('content_chunk', '')
                })

        return schedule_data

    def _extract_appointment_data(self, docs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract appointment-related data from documents"""
        appointment_data = {'requests': [], 'status': [], 'history': []}

        for doc in docs:
            event_type = doc.get('event_type', '')
            if 'appointment' in event_type:
                appointment_data['history'].append({
                    'type': event_type,
                    'date': doc.get('created_at'),
                    'content': doc.get('content_chunk', '')
                })

        return appointment_data

    def _extract_medication_data(self, docs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract medication-related data from documents"""
        medication_data = {'prescriptions': [], 'adherence': [], 'side_effects': []}

        for doc in docs:
            event_type = doc.get('event_type', '')
            if 'medication' in event_type:
                medication_data['prescriptions'].append({
                    'type': event_type,
                    'date': doc.get('created_at'),
                    'content': doc.get('content_chunk', '')
                })

        return medication_data

    def _extract_patient_data(self, docs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract patient-related data from documents"""
        patient_data = {'patients': [], 'registrations': []}

        for doc in docs:
            if doc.get('event_type') == 'patient_registered':
                patient_data['registrations'].append({
                    'date': doc.get('created_at'),
                    'content': doc.get('content_chunk', '')
                })

        return patient_data

    def _extract_symptom_data(self, docs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract symptom-related data from documents"""
        symptom_data = {'reports': [], 'assessments': []}

        for doc in docs:
            event_type = doc.get('event_type', '')
            if any(word in event_type for word in ['symptom', 'pain', 'vital']):
                symptom_data['reports'].append({
                    'type': event_type,
                    'date': doc.get('created_at'),
                    'content': doc.get('content_chunk', '')
                })

        return symptom_data

    def _extract_soap_data(self, docs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract SOAP note data from documents"""
        soap_data = {'notes': [], 'summaries': []}

        for doc in docs:
            if doc.get('event_type') in ['soap', 'soap_note_shared', 'consultation_notes']:
                soap_data['notes'].append({
                    'date': doc.get('created_at'),
                    'content': doc.get('content_chunk', ''),
                    'metadata': doc.get('metadata', {})
                })

        return soap_data
