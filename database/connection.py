"""
Database connection and initialization
"""
import logging
from supabase import create_client, Client

from config.settings import settings

logger = logging.getLogger(__name__)

# Global database client
supabase_client: Client = None

async def init_database():
    """Initialize database connection"""
    global supabase_client

    if settings.test_mode:
        logger.info("Test mode: Skipping database initialization")
        supabase_client = None
        return None

    try:
        logger.info("Initializing Supabase connection...")

        supabase_client = create_client(
            settings.supabase_url,
            settings.supabase_anon_key
        )

        # Connection established successfully
        logger.info("Database connection established successfully")
        return supabase_client

    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise RuntimeError(f"Database initialization failed: {e}")

def get_database() -> Client:
    """Get database client instance"""
    global supabase_client

    if settings.test_mode:
        return None

    if supabase_client is None:
        raise RuntimeError("Database not initialized. Call init_database() first.")

    return supabase_client
