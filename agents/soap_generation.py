"""
SOAP Notes Generation Agent - Enhanced for Structured Output
"""
import json
import logging
from typing import List, Dict, Any

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

from config.settings import settings
from models.schemas import (
    SOAPNotesStructured, SubjectiveSection, ObjectiveSection,
    AssessmentSection, PlanSection, PrimaryDiagnosis,
    DifferentialDiagnosis, ProblemListItem, FollowUpItem
)
from agents.specialty_detection import SpecialtyConfiguration
from utils.soap_parsing import SOAPParser

logger = logging.getLogger(__name__)

class SOAPNotesAgent:
    """Agent for generating structured SOAP notes from validated transcriptions"""

    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.openai_model,
            temperature=0.2,  # Lower temperature for more consistent medical documentation
            max_tokens=4000,  # Increased for structured output
            openai_api_key=settings.openai_api_key
        )
        self.parser = SOAPParser()

    async def generate_soap_notes(
        self,
        validated_text: str,
        patient_id: str,
        specialty_config: SpecialtyConfiguration,
        session_id: str,
        flags: List[Dict[str, Any]]
    ) -> SOAPNotesStructured:
        """
        Generate comprehensive structured SOAP notes from validated transcription

        Args:
            validated_text: Medically validated transcription
            patient_id: Patient identifier
            specialty_config: Dynamic specialty configuration from detection agent
            session_id: Recording session ID
            flags: Validation flags from previous step

        Returns:
            SOAPNotesStructured object with detailed clinical documentation
        """
        system_prompt = self._get_soap_system_prompt(specialty_config)
        user_prompt = self._get_soap_user_prompt(
            validated_text, patient_id, specialty_config, session_id, flags
        )
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        try:
            response = await self.llm.agenerate([messages])
            result_text = response.generations[0][0].text

            soap_notes = self.parser.parse_enhanced_soap_response(
                result_text, session_id, specialty_config.specialty
            )

            logger.info(f"Structured SOAP notes generated for session {session_id}")
            return soap_notes

        except Exception as e:
            logger.error(f"SOAP generation failed: {e}")
            return self.parser._create_fallback_soap_notes(None, session_id, specialty_config.specialty)
    
    def _get_soap_system_prompt(self, specialty_config: SpecialtyConfiguration) -> str:
        """Get system prompt for structured SOAP generation"""
        focus_areas = ', '.join(specialty_config.focus_areas)
        required_sections = ', '.join(specialty_config.required_sections)
        common_medications = ', '.join(specialty_config.common_medications)
        vital_signs_focus = ', '.join(specialty_config.vital_signs_focus)

        return f"""You are a {specialty_config.specialty.title()} Medical Documentation Expert. Your task is to generate comprehensive structured SOAP notes from validated medical transcriptions.

SPECIALTY CONFIGURATION:
- Specialty: {specialty_config.specialty}
- Focus Areas: {focus_areas}
- Required Sections: {required_sections}
- Common Medications: {common_medications}
- Vital Signs Focus: {vital_signs_focus}

Generate detailed SOAP notes in the following structured format:

SUBJECTIVE SECTION:
- Chief Complaint: Primary reason for visit
- History of Present Illness: Detailed chronological account
- Review of Systems: Systematic review by body system
- Past Medical History: Relevant medical history
- Medications: Current medications with dosages
- Allergies: Known allergies and reactions
- Social History: Relevant social factors

OBJECTIVE SECTION:
- Vital Signs: Complete vital signs with normal ranges
- Physical Exam: Systematic examination findings
- Diagnostic Results: Lab results, imaging, tests
- Mental Status: Cognitive and psychological assessment
- Functional Status: Mobility and daily living activities

ASSESSMENT SECTION:
- Primary Diagnosis: Main diagnosis with ICD-10 code
- Differential Diagnoses: Alternative diagnoses to consider
- Problem List: Active, resolved, and chronic problems
- Risk Assessment: Overall risk level and factors
- Prognosis: Expected clinical course

PLAN SECTION:
- Diagnostic Workup: Additional tests or studies needed
- Treatments: Therapeutic interventions
- Medications: Prescribed medications with instructions
- Follow-up: Scheduled appointments and monitoring
- Patient Education: Information provided to patient
- Referrals: Specialist consultations needed

OUTPUT FORMAT:
Return ONLY valid JSON in this exact structure:
{{
  "subjective": {{
    "chief_complaint": "string",
    "history_present_illness": "string",
    "review_of_systems": ["system1", "system2"],
    "past_medical_history": ["condition1", "condition2"],
    "medications": ["med1", "med2"],
    "allergies": ["allergy1", "allergy2"],
    "social_history": "string"
  }},
  "objective": {{
    "vital_signs": {{"bp": "value", "hr": "value", "temp": "value", "rr": "value"}},
    "physical_exam": {{"system": "findings"}},
    "diagnostic_results": ["result1", "result2"],
    "mental_status": "string",
    "functional_status": "string"
  }},
  "assessment": {{
    "primary_diagnosis": {{
      "diagnosis": "string",
      "icd10_code": "string",
      "confidence": 0.0-1.0,
      "severity": "mild/moderate/severe",
      "clinical_reasoning": "string"
    }},
    "differential_diagnoses": [
      {{
        "diagnosis": "string",
        "icd10_code": "string",
        "probability": 0.0-1.0,
        "ruling_out_criteria": "string"
      }}
    ],
    "problem_list": [
      {{
        "problem": "string",
        "status": "active/resolved/chronic",
        "priority": "high/medium/low"
      }}
    ],
    "risk_level": "low/moderate/high",
    "risk_factors": ["factor1", "factor2"],
    "prognosis": "string"
  }},
  "plan": {{
    "diagnostic_workup": ["test1", "test2"],
    "treatments": ["treatment1", "treatment2"],
    "medications": ["medication1", "medication2"],
    "follow_up": [
      {{
        "provider": "string",
        "timeframe": "string",
        "urgency": "routine/urgent/stat"
      }}
    ],
    "patient_education": ["education1", "education2"],
    "referrals": ["referral1", "referral2"]
  }},
  "clinical_notes": "Concise clinical summary"
}}

Be thorough, clinically accurate, and specialty-appropriate in your documentation."""
    
    def _get_soap_user_prompt(
        self,
        validated_text: str,
        patient_id: str,
        specialty_config: SpecialtyConfiguration,
        session_id: str,
        flags: List[Dict[str, Any]]
    ) -> str:
        """Get user prompt for structured SOAP generation"""
        flags_summary = self._summarize_flags(flags)

        return f"""Generate structured SOAP notes from this validated transcription:

TRANSCRIPTION:
{validated_text}

CONTEXT:
- Patient ID: {patient_id}
- Detected Specialty: {specialty_config.specialty} (confidence: {specialty_config.confidence:.2f})
- Session ID: {session_id}
- Validation Flags: {flags_summary}

SPECIALTY-SPECIFIC REQUIREMENTS:
- Focus Areas: {', '.join(specialty_config.focus_areas)}
- Required Sections: {', '.join(specialty_config.required_sections)}
- Common Medications: {', '.join(specialty_config.common_medications)}

Please create comprehensive structured SOAP notes following the exact JSON format specified in the system prompt."""
    

    

    
    def _summarize_flags(self, flags: List[Dict[str, Any]]) -> str:
        """Summarize validation flags for context"""
        if not flags:
            return "No validation flags"

        flag_summary = []
        for flag in flags:
            if isinstance(flag, dict):
                flag_type = flag.get('type', 'unknown')
                message = flag.get('message', 'No message')
                flag_summary.append(f"{flag_type}: {message}")
            else:
                # Handle string flags
                flag_summary.append(str(flag))

        return "; ".join(flag_summary)
