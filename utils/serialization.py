"""
JSON Serialization Utilities for Enhanced SOAP Generation System
Handles proper serialization of complex dataclass objects
"""
import json
import logging
from datetime import datetime, timezone
from dataclasses import asdict, is_dataclass
from typing import Any, Dict, List, Union
from decimal import Decimal

from models.schemas import SOAPNotes, SOAPNotesStructured, QualityMetrics

logger = logging.getLogger(__name__)

class EnhancedJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder for enhanced SOAP system objects"""
    
    def default(self, obj):
        """Convert objects to JSON-serializable format"""
        
        # Handle dataclasses
        if is_dataclass(obj):
            return asdict(obj)
        
        # Handle datetime objects
        if isinstance(obj, datetime):
            return obj.isoformat()
        
        # Handle Decimal objects
        if isinstance(obj, Decimal):
            return float(obj)
        
        # Handle sets
        if isinstance(obj, set):
            return list(obj)
        
        # Call the default method for other types
        return super().default(obj)

def serialize_soap_notes(soap_notes: SOAPNotes) -> Dict[str, Any]:
    """
    Serialize SOAPNotes object to JSON-compatible dictionary
    
    Args:
        soap_notes: SOAPNotes object to serialize
        
    Returns:
        JSON-serializable dictionary
    """
    try:
        return {
            "soap_notes": {
                "subjective": asdict(soap_notes.soap_notes.subjective),
                "objective": asdict(soap_notes.soap_notes.objective),
                "assessment": asdict(soap_notes.soap_notes.assessment),
                "plan": asdict(soap_notes.soap_notes.plan),
                "clinical_notes": soap_notes.soap_notes.clinical_notes
            },
            "quality_metrics": asdict(soap_notes.quality_metrics),
            "session_id": soap_notes.session_id,
            "specialty": soap_notes.specialty,
            
            # Legacy compatibility
            "subjective": soap_notes.subjective,
            "objective": soap_notes.objective,
            "assessment": soap_notes.assessment,
            "plan": soap_notes.plan,
            "icd_codes": soap_notes.icd_codes
        }
    except Exception as e:
        logger.error(f"Error serializing SOAP notes: {e}")
        return {
            "error": "Failed to serialize SOAP notes",
            "session_id": getattr(soap_notes, 'session_id', 'unknown'),
            "specialty": getattr(soap_notes, 'specialty', 'unknown')
        }

def serialize_processing_result(result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Serialize complete processing result to JSON-compatible format
    
    Args:
        result: Processing result dictionary
        
    Returns:
        JSON-serializable dictionary
    """
    try:
        serialized = {}
        
        for key, value in result.items():
            if key == "soap_notes" and hasattr(value, 'soap_notes'):
                # Handle SOAPNotes object
                serialized[key] = serialize_soap_notes(value)
            elif is_dataclass(value):
                # Handle other dataclass objects
                serialized[key] = asdict(value)
            elif isinstance(value, (datetime, Decimal)):
                # Handle special types
                serialized[key] = EnhancedJSONEncoder().default(value)
            elif isinstance(value, dict):
                # Recursively handle nested dictionaries
                serialized[key] = serialize_processing_result(value)
            elif isinstance(value, list):
                # Handle lists that might contain dataclasses
                serialized[key] = [
                    asdict(item) if is_dataclass(item) else item
                    for item in value
                ]
            else:
                # Keep as-is for basic types
                serialized[key] = value
        
        return serialized
        
    except Exception as e:
        logger.error(f"Error serializing processing result: {e}")
        return {
            "error": "Failed to serialize processing result",
            "original_keys": list(result.keys()) if isinstance(result, dict) else "not_dict"
        }

def safe_json_response(data: Any) -> Dict[str, Any]:
    """
    Create a safe JSON response from any data

    Args:
        data: Data to serialize

    Returns:
        JSON-serializable dictionary
    """
    try:
        # First try to serialize with our enhanced encoder
        if isinstance(data, dict):
            return serialize_processing_result(data)
        elif hasattr(data, 'soap_notes'):
            return serialize_soap_notes(data)
        elif is_dataclass(data):
            return asdict(data)
        else:
            # Test if it's already JSON serializable
            json.dumps(data, cls=EnhancedJSONEncoder)
            return data
    except (TypeError, ValueError) as e:
        logger.warning(f"Data not directly serializable, attempting conversion: {e}")

        if isinstance(data, dict):
            return serialize_processing_result(data)
        elif hasattr(data, 'soap_notes'):
            return serialize_soap_notes(data)
        elif is_dataclass(data):
            return asdict(data)
        else:
            return {
                "error": "Unable to serialize data",
                "data_type": str(type(data)),
                "message": str(e)
            }

def create_api_response(
    status: str,
    message: str,
    data: Any = None,
    error: str = None
) -> Dict[str, Any]:
    """
    Create a standardized API response

    Args:
        status: Response status ('success', 'error', etc.)
        message: Response message
        data: Response data (will be serialized)
        error: Error message if applicable

    Returns:
        JSON-serializable response dictionary
    """
    response = {
        "status": status,
        "message": message,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

    if data is not None:
        # Ensure data is properly serialized
        try:
            response["data"] = safe_json_response(data)
        except Exception as e:
            logger.error(f"Failed to serialize response data: {e}")
            response["data"] = {"error": f"Serialization failed: {str(e)}"}

    if error is not None:
        response["error"] = error

    # Test the entire response for JSON serializability
    try:
        json.dumps(response, cls=EnhancedJSONEncoder)
    except Exception as e:
        logger.error(f"Response not JSON serializable: {e}")
        return {
            "status": "error",
            "message": "Response serialization failed",
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    return response

def serialize_for_database(obj: Any) -> Dict[str, Any]:
    """
    Serialize object for database storage
    
    Args:
        obj: Object to serialize for database
        
    Returns:
        Database-compatible dictionary
    """
    try:
        if is_dataclass(obj):
            return asdict(obj)
        elif isinstance(obj, dict):
            return {
                key: serialize_for_database(value) if is_dataclass(value) else value
                for key, value in obj.items()
            }
        elif isinstance(obj, list):
            return [
                serialize_for_database(item) if is_dataclass(item) else item
                for item in obj
            ]
        else:
            return obj
    except Exception as e:
        logger.error(f"Error serializing for database: {e}")
        return {"error": f"Serialization failed: {str(e)}"}

# Convenience functions for common serialization tasks
def to_json_string(obj: Any, indent: int = 2) -> str:
    """Convert object to JSON string"""
    try:
        return json.dumps(safe_json_response(obj), cls=EnhancedJSONEncoder, indent=indent)
    except Exception as e:
        logger.error(f"Error converting to JSON string: {e}")
        return json.dumps({"error": f"JSON conversion failed: {str(e)}"}, indent=indent)

def from_json_string(json_str: str) -> Any:
    """Parse JSON string to object"""
    try:
        return json.loads(json_str)
    except Exception as e:
        logger.error(f"Error parsing JSON string: {e}")
        return {"error": f"JSON parsing failed: {str(e)}"}
