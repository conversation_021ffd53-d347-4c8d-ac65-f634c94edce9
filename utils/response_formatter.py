"""
Response formatting utilities for structured RAG responses
"""
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Union
from enum import Enum

logger = logging.getLogger(__name__)

class ResponseType(Enum):
    """Enumeration of supported response types"""
    LIST = "list"
    TEXT = "text"
    TIME_SLOTS = "time_slots"
    MEDICATION = "medication"
    APPOINTMENT_STATUS = "appointment_status"
    SOAP_NOTE = "soap_note"
    RESCHEDULE_REQUEST = "reschedule_request"
    PATIENT_LIST = "patient_list"
    SCHEDULE_OVERVIEW = "schedule_overview"
    SYMPTOM_ANALYSIS = "symptom_analysis"
    TREATMENT_PROGRESS = "treatment_progress"
    MEDICATION_SCHEDULE = "medication_schedule"
    VITAL_SIGNS = "vital_signs"

class ResponseFormatter:
    """Utility class for formatting structured responses"""
    
    @staticmethod
    def create_structured_response(
        response_type: Union[ResponseType, str],
        summary: str,
        data: Dict[str, Any],
        timestamp: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a structured response object
        
        Args:
            response_type: Type of response
            summary: Brief summary of the response
            data: Structured data specific to the response type
            timestamp: Optional timestamp (defaults to current time)
            
        Returns:
            Structured response dictionary
        """
        if isinstance(response_type, ResponseType):
            response_type = response_type.value
        
        return {
            "type": response_type,
            "summary": summary,
            "data": data,
            "timestamp": timestamp or datetime.now(timezone.utc).isoformat()
        }
    
    @staticmethod
    def format_time_slots_response(
        available_slots: List[Dict[str, Any]],
        booked_slots: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Format time slots response for doctors"""
        booked_slots = booked_slots or []
        
        data = {
            "available_count": len(available_slots),
            "booked_count": len(booked_slots),
            "available_slots": available_slots,
            "booked_slots": booked_slots,
            "next_available": available_slots[0] if available_slots else None
        }
        
        summary = f"{len(available_slots)} available slots, {len(booked_slots)} booked"
        
        return ResponseFormatter.create_structured_response(
            ResponseType.TIME_SLOTS, summary, data
        )
    
    @staticmethod
    def format_medication_response(
        medications: List[Dict[str, Any]],
        adherence_data: List[Dict[str, Any]] = None,
        side_effects: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Format medication information response"""
        adherence_data = adherence_data or []
        side_effects = side_effects or []
        
        data = {
            "current_medications": medications,
            "medication_count": len(medications),
            "adherence_reports": adherence_data,
            "side_effects": side_effects,
            "last_updated": datetime.now(timezone.utc).isoformat()
        }
        
        summary = f"{len(medications)} current medications"
        if adherence_data:
            summary += f", {len(adherence_data)} adherence reports"
        if side_effects:
            summary += f", {len(side_effects)} side effects reported"
        
        return ResponseFormatter.create_structured_response(
            ResponseType.MEDICATION, summary, data
        )
    
    @staticmethod
    def format_appointment_status_response(
        status: str,
        appointment_details: Dict[str, Any],
        upcoming_appointments: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Format appointment status response for patients"""
        upcoming_appointments = upcoming_appointments or []
        
        data = {
            "current_status": status,
            "appointment_details": appointment_details,
            "upcoming_appointments": upcoming_appointments,
            "upcoming_count": len(upcoming_appointments)
        }
        
        summary = f"Appointment status: {status}"
        if upcoming_appointments:
            summary += f", {len(upcoming_appointments)} upcoming"
        
        return ResponseFormatter.create_structured_response(
            ResponseType.APPOINTMENT_STATUS, summary, data
        )
    
    @staticmethod
    def format_patient_list_response(
        patients: List[Dict[str, Any]],
        total_count: int = None
    ) -> Dict[str, Any]:
        """Format patient list response for doctors"""
        total_count = total_count or len(patients)
        
        data = {
            "patients": patients,
            "displayed_count": len(patients),
            "total_count": total_count,
            "has_more": len(patients) < total_count
        }
        
        summary = f"{len(patients)} patients"
        if total_count > len(patients):
            summary += f" (showing {len(patients)} of {total_count})"
        
        return ResponseFormatter.create_structured_response(
            ResponseType.PATIENT_LIST, summary, data
        )
    
    @staticmethod
    def format_soap_note_response(
        soap_notes: List[Dict[str, Any]],
        latest_note: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Format SOAP notes response"""
        latest_note = latest_note or (soap_notes[0] if soap_notes else None)
        
        data = {
            "soap_notes": soap_notes,
            "notes_count": len(soap_notes),
            "latest_note": latest_note,
            "date_range": {
                "earliest": min(note.get("date", "") for note in soap_notes) if soap_notes else None,
                "latest": max(note.get("date", "") for note in soap_notes) if soap_notes else None
            }
        }
        
        summary = f"{len(soap_notes)} SOAP notes available"
        if latest_note:
            summary += f", latest from {latest_note.get('date', 'unknown date')}"
        
        return ResponseFormatter.create_structured_response(
            ResponseType.SOAP_NOTE, summary, data
        )
    
    @staticmethod
    def format_symptom_analysis_response(
        symptoms: List[Dict[str, Any]],
        severity_trends: Dict[str, Any] = None,
        recommendations: List[str] = None
    ) -> Dict[str, Any]:
        """Format symptom analysis response"""
        severity_trends = severity_trends or {}
        recommendations = recommendations or []
        
        data = {
            "symptoms": symptoms,
            "symptom_count": len(symptoms),
            "severity_trends": severity_trends,
            "recommendations": recommendations,
            "analysis_date": datetime.now(timezone.utc).isoformat()
        }
        
        summary = f"{len(symptoms)} symptoms tracked"
        if severity_trends:
            summary += ", trends available"
        
        return ResponseFormatter.create_structured_response(
            ResponseType.SYMPTOM_ANALYSIS, summary, data
        )
    
    @staticmethod
    def format_text_response(
        text: str,
        source_count: int = 0,
        confidence: float = None
    ) -> Dict[str, Any]:
        """Format simple text response"""
        data = {
            "response": text,
            "source_count": source_count,
            "confidence": confidence,
            "word_count": len(text.split()) if text else 0
        }
        
        summary = f"Text response ({len(text.split())} words)" if text else "Empty response"
        if source_count > 0:
            summary += f" from {source_count} sources"
        
        return ResponseFormatter.create_structured_response(
            ResponseType.TEXT, summary, data
        )
    
    @staticmethod
    def format_list_response(
        items: List[Any],
        list_type: str = "general",
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Format list response"""
        metadata = metadata or {}
        
        data = {
            "items": items,
            "count": len(items),
            "list_type": list_type,
            "metadata": metadata
        }
        
        summary = f"{len(items)} {list_type} items"
        
        return ResponseFormatter.create_structured_response(
            ResponseType.LIST, summary, data
        )
    
    @staticmethod
    def validate_response_structure(response: Dict[str, Any]) -> bool:
        """
        Validate that a response has the correct structure
        
        Args:
            response: Response dictionary to validate
            
        Returns:
            True if valid, False otherwise
        """
        required_fields = ["type", "summary", "data", "timestamp"]
        
        if not isinstance(response, dict):
            return False
        
        for field in required_fields:
            if field not in response:
                logger.warning(f"Missing required field: {field}")
                return False
        
        # Validate response type
        valid_types = [rt.value for rt in ResponseType]
        if response["type"] not in valid_types:
            logger.warning(f"Invalid response type: {response['type']}")
            return False
        
        return True
    
    @staticmethod
    def sanitize_response_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize response data to ensure it's JSON serializable
        
        Args:
            data: Data dictionary to sanitize
            
        Returns:
            Sanitized data dictionary
        """
        try:
            # Test JSON serialization
            json.dumps(data)
            return data
        except (TypeError, ValueError) as e:
            logger.warning(f"Data not JSON serializable, sanitizing: {e}")
            
            # Convert to JSON serializable format
            sanitized = {}
            for key, value in data.items():
                try:
                    json.dumps(value)
                    sanitized[key] = value
                except (TypeError, ValueError):
                    sanitized[key] = str(value)
            
            return sanitized
