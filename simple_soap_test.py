#!/usr/bin/env python3
"""
Simple SOAP RAG Test - Test basic functionality
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"
DOCTOR_ID = "dr_test_123"
PATIENT_ID = "patient_test_456"

def test_simple_search():
    """Test simple search queries"""
    print("🔍 Testing Simple Search Queries...")
    
    # Test doctor search with simple terms
    simple_queries = [
        "John Smith",
        "chest pain", 
        "SOAP note",
        "patient",
        "diagnosis"
    ]
    
    for query in simple_queries:
        print(f"\n🔍 Testing query: '{query}'")
        
        # Doctor search
        doctor_data = {
            "doctor_id": DOCTOR_ID,
            "query": query,
            "similarity_threshold": 0.3  # Lower threshold
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/v1/rag/search/doctor", json=doctor_data, timeout=30)
            result = response.json()
            
            if result.get("success"):
                doc_count = result.get("relevant_documents_count", 0)
                response_text = result.get("response", "")
                print(f"   ✅ Doctor search: Found {doc_count} documents")
                if doc_count > 0:
                    print(f"   📄 Response: {response_text[:100]}...")
                else:
                    print(f"   ❌ No documents found")
            else:
                print(f"   ❌ Doctor search failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"   ❌ Doctor search error: {e}")
        
        # Patient search
        patient_data = {
            "patient_id": PATIENT_ID,
            "query": query,
            "similarity_threshold": 0.3  # Lower threshold
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/v1/rag/search/patient", json=patient_data, timeout=30)
            result = response.json()
            
            if result.get("success"):
                doc_count = result.get("relevant_documents_count", 0)
                response_text = result.get("response", "")
                print(f"   ✅ Patient search: Found {doc_count} documents")
                if doc_count > 0:
                    print(f"   📄 Response: {response_text[:100]}...")
                else:
                    print(f"   ❌ No documents found")
            else:
                print(f"   ❌ Patient search failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"   ❌ Patient search error: {e}")

def test_knowledge_base_stats():
    """Test if we can get knowledge base stats"""
    print("\n📊 Testing Knowledge Base Stats...")
    
    # Try a very broad search to see what's in the knowledge base
    broad_queries = ["medical", "note", "data", "information"]
    
    for query in broad_queries:
        doctor_data = {
            "doctor_id": DOCTOR_ID,
            "query": query,
            "similarity_threshold": 0.1,  # Very low threshold
            "max_results": 10
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/v1/rag/search/doctor", json=doctor_data, timeout=30)
            result = response.json()
            
            if result.get("success"):
                doc_count = result.get("relevant_documents_count", 0)
                print(f"   Query '{query}': {doc_count} documents")
                
                if doc_count > 0:
                    # Show first document details
                    docs = result.get("relevant_documents", [])
                    if docs:
                        first_doc = docs[0]
                        print(f"   📄 First doc event_type: {first_doc.get('event_type', 'unknown')}")
                        print(f"   📄 First doc similarity: {first_doc.get('similarity_score', 0):.3f}")
                        content = first_doc.get('content', '')[:200]
                        print(f"   📄 Content preview: {content}...")
                    break  # Found something, stop searching
                    
        except Exception as e:
            print(f"   ❌ Error with query '{query}': {e}")

if __name__ == "__main__":
    print("🚀 Starting Simple SOAP RAG Test...")
    print(f"Testing against: {BASE_URL}")
    print(f"Doctor ID: {DOCTOR_ID}")
    print(f"Patient ID: {PATIENT_ID}")
    
    # Wait a moment for any recent embeddings to settle
    time.sleep(2)
    
    test_knowledge_base_stats()
    test_simple_search()
    
    print("\n✅ Simple test completed!")
